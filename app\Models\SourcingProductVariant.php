<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SourcingProductVariant extends Model
{
    protected $table = "seller_sourcing_product_variants";

    public $timestamps = true;

    protected $fillable = [
        'seller_id',
        'seller_name',
        'sourcing_request_id',
        'sourcing_request_code',
        'product_id',
        'name',
        'value',
    ];

    public function product()
    {
        return $this->belongsTo(SourcingProduct::class, 'product_id');
    }
}