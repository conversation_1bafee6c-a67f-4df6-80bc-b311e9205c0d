<?php

namespace App\Services\Kpis;

use App\Services\Orders\FilterOrders;
use GlobalHelper;

class KpisOrders
{
    protected FilterOrders $filterOrders;

    public function __construct()
    {
        $this->filterOrders = new FilterOrders();
    }

    /*
        Prepare base data for filtering orders
    */
    private function getBaseData($data)
    {
        return array_merge($data, [
            'excludedOrderTypes' => ['followup', 'firstmile'],
            'count' => 1
        ]);
    }

    /*
        Get total leads and default rate (100%)
    */
    public function kpisAll($data){
        $data = $this->getBaseData($data);
        $count = (float) $this->filterOrders->getOrders($data);
        return [
            'count' => $count,
            'rate' => 100
        ];
    }

    /*
        Get total real orders and their rate
    */
    public function kpisReals($data){
        $data = $this->getBaseData($data);
        $totalOrders = $data['totalOrders'] ?? $this->kpisAll($data)['count'];

        $dataReals = array_merge($data, [
            'excludedStatus' => ['newlead', 'wrongphonenumber', 'test', 'doubleorder', 'pending']
        ]);

        $count = (float) $this->filterOrders->getOrders($dataReals);
        $rate = GlobalHelper::calculRate(['total' => $count, 'baseTotal' => $totalOrders]);

        return [
            'count' => $count,
            'rate' => GlobalHelper::formatNumber($rate)
        ];
    }

    /*
        Get total and rate for "new" status
    */
    public function kpisStatus($data,$type,$options = []){
        $onlyCount = $options['onlyCount'] ?? null;

        // Get Count
        if($onlyCount == 1){
            return $this->getCountOrders($data, $type);
        }else{
            $baseRate = "reals";
            if(in_array($type,["processing"])){
                $baseRate = "confirmed";
            }
            if(in_array($type,["intransit"])){
                $baseRate = "shipped";
            }
            return $this->getStatusResult($data,$type,$baseRate);
        }
        
    }

    /*
        Kpis Confirmed
    */
    public function kpisConfirmed($data,$options = []){
        $onlyCount = $options['onlyCount'] ?? null;
        
        // Get Count
        if($onlyCount == 1){
            return $this->getCountOrders($data, ['confirmed' => 'yes']);
        }else{
            // Get Result Confirmed
            $results = $this->getStatusResult($data, ['confirmed' => 'yes'], 'realsconfirm');
            $results['rateReals'] = $this->getStatusResult($data, ['confirmed' => 'yes'], 'leads')['rate']; 

            // Return Results
            return $results;
        }
    }

    /*
       Kpis Shipped
    */
    public function kpisShipped($data,$options = []){
        $onlyCount = $options['onlyCount'] ?? null;
        $listStatus =  ['delivered', 'return', 'intransit'];

        // Get Count
        if($onlyCount == 1){
            return $this->getCountOrders($data, ['listStatus' => $listStatus]);
        }else{
            $results = $this->getStatusResult($data, ['listStatus' => $listStatus], 'confirmed');
            return $results;
        }
    }

    /*
       Kpis Others
    */
    public function kpisOthers($data,$options = []){
        $onlyCount = $options['onlyCount'] ?? null;
        $listStatus = ['schedule', 'wrongphonenumber', 'doubleorder','pending','test'];

        // Get Count
        if($onlyCount == 1){
            return $this->getCountOrders($data, ['listStatus' => $listStatus]);
        }else{
            $results = $this->getStatusResult($data, ['listStatus' => $listStatus], 'leads');
            return $results;
        }
    }

    /*
        Kpis Confirmed
    */
    public function kpisDelivered($data,$options = []){
         $onlyCount = $options['onlyCount'] ?? null;
        
        // Get Count
        if($onlyCount == 1){
            return $this->getCountOrders($data, 'delivered');
        }else{
            $results = $this->getStatusResult($data, 'delivered', 'finished');
            $results['rateReals'] = $this->getStatusResult($data, 'delivered', 'shipped')['rate'];
            return $results;
        }
    }


    /*
        Kpis Confirmed
    */
    public function kpisReturn($data,$options = []){
         $onlyCount = $options['onlyCount'] ?? null;
        
        // Get Count
        if($onlyCount == 1){
            return $this->getCountOrders($data, 'return');
        }else{
            $results = $this->getStatusResult($data, 'return', 'finished');
            $results['rateReals'] = $this->getStatusResult($data, 'return', 'shipped')['rate'];
            return $results;
        }
    }


    /*
        Get total and rate for "upselling" status (based on real orders)
    */
    public function kpisUpselling($data)
    {
        return $this->getStatusResult($data, ['upsell' => 'yes'], 'reals');
    }

    /*
        Generic helper: get total and rate for a given status
    */
    private function getStatusResult($data, $status, $base = 'leads'){
        $data = $this->getBaseData($data);
        $count = $this->getCountOrders($data, $status);
    
        // Base Total
        $baseTotal = match ($base) {
            'reals' => $this->kpisReals($data)['count'],
            'confirmed' => $this->kpisConfirmed($data,["onlyCount" => 1]),
            'realsconfirm' =>$this->kpisReals($data)['count'] - $this->kpisStatus($data,"schedule",["onlyCount" => 1]),
            'shipped' => $this->kpisShipped($data,["onlyCount" => 1]),
            'finished' => $this->kpisDelivered($data,["onlyCount" => 1]) + $this->kpisReturn($data,["onlyCount" => 1]),
            default => $this->kpisAll($data)['count'],
        };

        // Calcul Rate
        $rate = GlobalHelper::calculRate(['total' => $count, 'baseTotal' => $baseTotal]);

        // Return Result
        return [
            'count' => $count,
            'rate' => $rate,
        ];
    }

    /*
        Generic helper: get only the total count for a status
    */
    private function getCountOrders($data, $status){
        $query = is_array($status)
            ? array_merge($data, $status)
            : array_merge($data, ['status' => $status]);

        return (float) $this->filterOrders->getOrders($query);
    }
}
