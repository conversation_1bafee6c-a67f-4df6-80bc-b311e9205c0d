<?php
namespace App\Services\Shipping;

use App\Models\Warehouses;
use App\Services\Fields\FieldsService;
use App\Traits\FilterQuery;

class FilterWarehouses {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $columnsType;
    use FilterQuery;
    
    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();

        // Table name
        $this->baseTable = 'warehouse';
        $this->columnsType = 'warehouse';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getWarehouses(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Custom orders
        $this->data['ordering'] = $this->data['ordering'] ?? "asc";
        
        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // General Filter
        $this->applyGeneralFilters();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();
        
        // Fetch and return the final filtered results
        return $this->fetchResults();
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = Warehouses::query();
    }

    /**
     * Apply general filters to the query.
     */
    private function applyGeneralFilters(){
        // Get the parent filter value from data, default to null if not set
        $id = $this->data["id"] ?? null;
        $listeIds = $this->data["listeIds"] ?? null;
        
        // Filter By id
        if($id){ $this->query->where("{$this->baseTable}.id", $id); }
        if($listeIds){ $this->query->whereIn("{$this->baseTable}.id", $listeIds); }
    }
}