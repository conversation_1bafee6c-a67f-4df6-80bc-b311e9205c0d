<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sourcing_journals', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('user_name');
            $table->unsignedBigInteger('sourcing_id');
            $table->string('sourcing_code');
            $table->string('sourcing_seller');
            $table->string('sourcing_status');
            $table->string('action');
            $table->timestamps();

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('sourcing_id')->references('id')->on('sellers_sourcing_request')->onDelete('cascade');

            // Indexes
            $table->index('sourcing_id');
            $table->index('user_id');
            $table->index('action');
            $table->index('sourcing_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sourcing_journals');
    }
};