<?php
namespace App\Services\Kpis;

use App\Services\Orders\OrderStatusService;
use App\Services\Orders\FilterOrders;
use GlobalHelper;

class KpisFunds {

    protected FilterOrders $filterOrders;
    protected OrderStatusService $OrderStatusService;
    
    public function __construct() {
        $this->filterOrders = new FilterOrders();
        $this->OrderStatusService = new OrderStatusService();
    }

    /**
     * net Funds
     */
    public function fundsAll($data) {
        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);

        // Calcul Amount and count
        $totalCount = (float) $this->filterOrders->getOrders(array_merge($data,['count' => 1]));
        $totalAmount = (float) $this->filterOrders->getOrders(array_merge($data,['totalPrices' => 1]));
       
        // Return the formatted final data containing all KPIs and their values
        return [
            'totalCount' => $totalCount,
            'totalAmount' => GlobalHelper::formatNumber($totalAmount),
        ];
    }

    /**
     * Confirmed Funds
     */
    public function fundsConfirmed($data) {
        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);
        $data["confirmed"] = "yes";

        // Calcul Amount and count
        $totalCount = (float) $this->filterOrders->getOrders(array_merge($data,['count' => 1]));
        $totalAmount = (float) $this->filterOrders->getOrders(array_merge($data,['totalPrices' => 1]));
       
        // Return the formatted final data containing all KPIs and their values
        return [
            'totalCount' => $totalCount,
            'totalAmount' => GlobalHelper::formatNumber($totalAmount),
        ];
    }

    /**
     * Delivered Funds
     */
    public function fundsDelivered($data) {
        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);
        $data["status"] = "delivered";

        // Calcul Amount and count
        $totalCount = (float) $this->filterOrders->getOrders(array_merge($data,['count' => 1]));
        $totalAmount = (float) $this->filterOrders->getOrders(array_merge($data,['totalPrices' => 1]));
       
        // Return the formatted final data containing all KPIs and their values
        return [
            'totalCount' => $totalCount,
            'totalAmount' => GlobalHelper::formatNumber($totalAmount),
        ];
    }

    /**
     * Returns the base data used for filtering orders.
     */
    private function getBaseData($data) {
        return array_merge($data,[
            'excludedOrderTypes' => ['followup', 'firstmile'],
        ]);
    }
}
