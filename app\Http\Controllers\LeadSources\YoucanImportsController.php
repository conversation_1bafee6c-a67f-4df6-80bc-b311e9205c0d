<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\YouCanService;
use App\Services\Orders\OrderAPIService;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SellersHelper as GlobalSellersHelper;

class YoucanImportsController extends Controller
{
    protected $youcanService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->youcanService = new YouCanService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }

    /**
     * Install YouCan App - Redirect to authorization page.
     */
    public function install(Request $request){
        $redirectUri = env('YOUCAN_REDIRECT_URI');
        $scopes = [
            "read-orders",
            "edit-rest-hooks"
        ];
        $state = encrypt($this->currentSeller->id);

        $installUrl = "https://seller-area.youcan.shop/admin/oauth/authorize?"
            . "client_id=" . env('YOUCAN_API_KEY')
            . "&redirect_uri=" . urlencode($redirectUri)
            . "&response_type=code"
            . "&scope=" . implode('%20', array_map('urlencode', $scopes)) // Encode each scope
            ."&state={$state}";

            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ], 200);
    }


    /**
     * Handle OAuth Callback - Get Access Token and Register Webhooks
     */
    public function callback(Request $request)
    {
         // Validate request parameters
         $validationResponse = $this->youcanService->validateRequestParameters($request, ['code','state']);
         $data = $validationResponse->getData(true);

         if ($data['response'] === 'error') {
             return $validationResponse; // Return the error response if validation failed
         }
         $code = $data['result']['code'];
         $state = $data['result']['state'];

        $errorResponse = $this->youcanService->handleOAuthError($request);
        $errorData = $errorResponse->getData(true);

        if ($errorData['response'] === 'error') {
            return $errorResponse;
        }

        $tokenService = $this->youcanService->getAccessToken($code);

        if ($tokenService->getData() === 'error') {
            return $tokenService; // Return the error response
        }

        $accessToken = $tokenService->getData()->result;
        //Get seller
        $seller = Sellers::find(decrypt($state));

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'YouCan',
            'api_token' => $accessToken,
            'seller' => $seller,
        ]);

         // Register webhook
         $webhookUrl = route('youcan.webhook', ['id' => decrypt($state)]); // Make sure you have this route defined

        $this->subscribeToWebhook($accessToken,$webhookUrl);

        return redirect()->away(env('URL_APP_SELLERS').'/lead-sources');
    }

    /**
     * Subscribe to order creation Webhook
     */
    private function subscribeToWebhook($accessToken,$webhookUrl)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
        ])->asForm()->post('https://api.youcan.shop/resthooks/subscribe', [
            'event'      => 'order.create',
            'target_url' => $webhookUrl,
        ]);

        if ($response->ok()) {
            return response()->json([
                'response' => 'success',
            ], 200);
        } else {
            return response()->json([
                'response' => 'error',
                'message' => $response->json(),
            ], 401);
        }
    }

     /**
     * Handle Order Webhook
     */
    public function handleOrderWebhook(Request $request,$id)
    {

        $request_from_youcan = $this->youcanService->isValidYouCanSignature($request);

        if ($request_from_youcan !== true) {
            return $request_from_youcan; // HACKER possibility here
        }

        // Formatter Data
        $orderData = $this->youcanService->formatWebHookOrder($request->all()
        );

        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $orderData,
            'sellerId' => $id
        ]);
 // Check if the response contains an error
 if ($responseOrders['response'] === 'error') {
    Log::error('Order API Service returned an error', [
        'seller_id' => $id,
        'order_data' => $orderData,
        'response' => $responseOrders
    ]);
    return response()->json($responseOrders, 400);
}
// Return Response
return response()->json($responseOrders, 200 );
    }





    /**
     * Retrieves a list of orders from YouCan.
     *
     * @param Request $request The incoming request instance.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the list of orders or an error message.
     */
    public function getOrders(Request $request)
    {
        // Retrieve YouCan API token (replace with your logic)
        $accessToken = $this->youcanService->checkYouCanToken();


         // Validate and collect base query parameters
         $queryParams = $request->only(['include', 'page', 'limit']);

         // Handling filters dynamically
         if ($request->has('filters')) {
             $filters = $request->input('filters');
             foreach ($filters as $index => $filter) {
                 if (isset($filter['field'], $filter['value'])) {
                     $queryParams["filters[$index][field]"] = $filter['field'];
                     $queryParams["filters[$index][value]"] = $filter['value'];
                 }
             }
         }

        // Make API request
        $response = Http::withToken($accessToken)
            ->get('https://api.youcan.shop/orders', $queryParams);

        // Handle response
        if ($response->failed()) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_fetch_orders_from_youcan'),
            ], $response->status());
        }

        return response()->json([
            'response' => 'success',
            'result' => $response->json(),
        ], 200);
    }
}