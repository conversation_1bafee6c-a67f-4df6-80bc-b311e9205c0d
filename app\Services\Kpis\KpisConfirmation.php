<?php
namespace App\Services\Kpis;
use App\Services\Orders\FilterOrders;
use GlobalHelper;

class KpisConfirmation {

    protected FilterOrders $filterOrders;
    public function __construct() {
        $this->filterOrders = new FilterOrders();
    }

    /**
     * Get Liste Statuts
     */
    public function confirmationKpis($data) {
        // Retrieve the type of date for filtering or calculation
        $dateType = $this->data['dateType'] ?? null;

        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);

        // Fetch the statuses for processing
        $statuses = $this->getStatuses();

        // Calculate the totals using the statuses and base data
        $totals = $this->calculateTotals($statuses, $data);

        // Calculate the total orders and real orders based on date type and totals
        list($totalOrders, $totalReals) = $this->calculateTotalOrders($dateType, $totals, $data);

        // Calculate the rates based on totals, total orders, and total real orders
        $rates = $this->calculateRates($totals, $totalOrders, $totalReals);

        // Return the formatted final data containing all KPIs and their values
        return $this->formatFinalData($totals, $rates, $totalOrders, $totalReals);
    }
    
    /**
     * Returns the base data used for filtering orders.
     */
    private function getBaseData($data) {
        return array_merge($data,[
            'excludedOrderTypes' => ['followup', 'firstmile'],
            'count' => 1
        ]);
    }
    
    /**
     * Returns the mapping of statuses used to categorize orders.
     */
    private function getStatuses() {
        return [
            'new' => 'newlead',
            'schedule' => 'schedule',
            'wrongPhone' => 'wrongphonenumber',
            'test' => 'test',
            'confirmed' => ['confirmed' => 'yes'],
            'upselling' => ['upsell' => 'yes'],
            'noAnswer' => 'noanswer',
            'duplicate' => 'doubleorder',
            'canceled' => 'canceled',
            'pending' => 'pending'
        ];
    }
    
    /**
     * Calculates the total number of orders for each status.
     */
    private function calculateTotals($statuses, $data) {
        $totals = [];
        foreach ($statuses as $key => $status) {
            $queryData = is_array($status) ? array_merge($data, $status) : array_merge($data, ['status' => $status]);
            $totals[$key] = (float) $this->filterOrders->getOrders($queryData);
        }
        return $totals;
    }
    
    /**
     * Calculates total orders and real orders based on the given date type.
     */
    private function calculateTotalOrders($dateType, $totals, $data) {
        if ($dateType === "statusDate") {
            $totalOrders = array_sum($totals) - $totals['confirmed'] - $totals['upselling'];
            $totalReals = $totalOrders - $totals['wrongPhone'] - $totals['test'] - $totals['duplicate'] - $totals['new'] - $totals['pending'];
        } else {
            $totalOrders = (float) $this->filterOrders->getOrders($data);
            $dataReals = array_merge($data, ['excludedStatus' => ['newlead', 'wrongphonenumber', 'test', 'doubleorder', 'pending']]);
            $totalReals = (float) $this->filterOrders->getOrders($dataReals);
        }
        return [$totalOrders, $totalReals];
    }
    
    /**
     * Calculates the rates for different order statuses.
     */
    private function calculateRates($totals, $totalOrders, $totalReals) {
        return [
            'reals' => GlobalHelper::calculRate(['total' => $totalReals, 'baseTotal' => $totalOrders]),
            'confirmed' => GlobalHelper::calculRate(['total' => $totals['confirmed'], 'baseTotal' => ($totalReals - $totals['schedule'])]),
            'confirmedReel' => GlobalHelper::calculRate(['total' => $totals['confirmed'], 'baseTotal' => $totalOrders]),
            'noAnswer' => GlobalHelper::calculRate(['total' => $totals['noAnswer'], 'baseTotal' => $totalReals]),
            'canceled' => GlobalHelper::calculRate(['total' => $totals['canceled'], 'baseTotal' => $totalReals]),
            'new' => GlobalHelper::calculRate(['total' => $totals['new'], 'baseTotal' => $totalOrders]),
            'upselling' => GlobalHelper::calculRate(['total' => $totals['upselling'], 'baseTotal' => $totalReals]),
            'pending' => GlobalHelper::calculRate(['total' => $totals['pending'], 'baseTotal' => $totalReals]),
            'schedule' => GlobalHelper::calculRate(['total' => $totals['schedule'], 'baseTotal' => $totalReals]),
            'wrongPhone' => GlobalHelper::calculRate(['total' => $totals['wrongPhone'], 'baseTotal' => $totalReals]),
            'test' => GlobalHelper::calculRate(['total' => $totals['test'], 'baseTotal' => $totalReals]),
            'duplicate' => GlobalHelper::calculRate(['total' => $totals['duplicate'], 'baseTotal' => $totalReals]),
        ];
    }
    
    /**
     * Formats the final output data containing totals and calculated rates.
     */
    private function formatFinalData($totals, $rates, $totalOrders, $totalReals) {
        $finalData = array_reduce(array_keys($totals), function ($carry, $key) use ($totals, $rates) {
            $carry["total" . ucfirst($key)] = $totals[$key];
            $carry["rate" . ucfirst($key)] = $rates[$key] ?? null;
            return $carry;
        }, []);
    
        return array_merge([
            'totalLeads' => $totalOrders,
            'rateLeads' => 100,
            'totalReals' => $totalReals,
            'rateReals' => $rates['reals'],
            'rateConfirmedReel' => $rates['confirmedReel'] // Ensure it's explicitly included
        ], $finalData);
    }
}