<?php

namespace App\Models;
use CacheHelper;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Sellers
 */
class Sellers extends Model
{
    protected $table = 'sellers';

    public $timestamps = true;

    protected $fillable = [
		'user_id',
		'statut',
		'fullname',
		'cin',
		'phone',
		'mobile',
		'city_id',
		'city_name',
		'address',
		'email',
		'email_pro',
		'name_pro',
		'password',
		'description',
		'facturation',
		'price_return',
		'type_price_livraison',
		'statut_stock',
		'bank',
		'rib',
		'bank_address',
		'bank_swift',
		'statut_validate',
		'orgine_created',
		'name_page',
		'barre_code',
		'facebook',
		'instagram',
		'website',
		'numpages',
		'type_juridique',
		'seller_code',
		'active_sms',
		'created_by',
		'updated_by',
		'deleted_by',
		'price_livraison_forfait',
		'price_upsell',
		'price_outboundcall',
		'price_callfollowup',
		'price_callcenter',
		'price_callcenter_delivered',
	    'account_manager_id',
	    'account_manager_name',
	    'confirmatio_email',
		'tva_pourc_value',
		'cod_declared_value',
		'cod_pourc_value',
		'clearance_declared_value',
		'clearance_pourc_value',
		'register_number',
		'tax_identification',
		'cnss_number',
		'ice_number',
		'patente',
		'fulfillment',
		'accept_conditions',
		'signature_path',
		'show_bjamla_menu',
		'view_video_demo',
		'manage_orders',
		'cod_type_calcul',
		'clearance_type_calcul',
		'cod_flaterate',
		'clearance_flaterate',
        'prepaid_fees',
		'theme_id',
		'manage_callcenter',
		'manage_shipping',
		'manage_followup',
		'manage_warehousing',
		'permissions',

		'customer_id',
		'current_subscription_id',
		'current_subscription_statut',
		'current_period_start',
		'current_period_end',
		'billing_type',
		'last_invoice_date',
		'type_googelsheet',
		'usedstores',

		'is_affiliate',
		'affiliate_type',
		'affiliate_id',
		'affiliate_username',
		'affiliate_password',

		'pixel_fb_enabled',
		'api_token',
		'statut_chatbot',
		'base_shipping',
		'webhook_url',

        'two_factor_enabled',
        'two_factor_secret',

    ];

    protected $guarded = [];

	public function user()
    {
        return $this->belongsTo(User::class);
    }
}