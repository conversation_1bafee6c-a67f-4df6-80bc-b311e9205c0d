<?php

namespace App\Services\LeadSource;

use App\Models\SellersApiImports;
use App\Models\SellersAutoImports;
use SellersHelper;

class LeadSourceService{
    /**
     * @var mixed The currently authenticated seller.
     */
    protected $currentSeller;

    /**
     * Constructor to initialize the current seller.
     */
    public function __construct(){
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Retrieve the API token for a given lead source.
     *
     * @param string $apiName The name of the API service.
     * @return string|null The API token or null if not found.
     */
    public function getLeadSourceToken(string $apiName): ?string{

        $row = SellersApiImports::where('seller_id', $this->currentSeller->id)
                                ->where('created_by_user_id', $this->currentSeller->user_id)
                                ->where('api_name', $apiName)
                                ->first();

        return $row?->api_token;
    }

    /**
     * Creates or updates a lead source token for the current seller based on provided data.
     * @param array $data The data required for creating or updating the lead source token.
     */
    public function createLeadSourceToken($data){
        // Extract data from the input
        $api_name = $data['api_name'] ?? null;
        $api_token = $data['api_token'] ?? null;
        $shopurl = $data['shopurl'] ?? null;
        $api_label = $data['api_label'] ?? null;
        $seller = $data['seller'] ?? null;

        // Check if essential values are provided
        if ($api_name) {
            // Check if the lead source already exists
            $existingLeadSource = $this->getExistingLeadSource($api_name, $shopurl, $api_token,$seller);

            // Prepare the data for insertion or update
            $dataLeadSource = [
                'seller_id' => $seller->id,
                'api_name' => $api_name,
                'api_token' => $api_token,
                'created_by_user_id' => $seller->user_id,
                'shopurl' => $shopurl,
                'api_label' => $api_label,
            ];

            // Update the existing record or create a new one
            if ($existingLeadSource) {
                $existingLeadSource->update($dataLeadSource);

            } else {
                do {
                    $newRecord = SellersApiImports::create($dataLeadSource);
                    $existsInAutoImports = SellersAutoImports::where('id', $newRecord->id)
                                            ->where('seller_id', $seller->id)
                                            ->exists();

                    if ($existsInAutoImports) {
                        $newRecord->delete();
                    }
                } while ($existsInAutoImports);
                // SellersApiImports::create($dataLeadSource);

            }


        }

    }

    /**
     * Retrieves an existing lead source based on the given API name, shop URL, and API token.
     */
    private function getExistingLeadSource($api_name, $shopurl, $api_token,$seller){
        // Build the query to find the lead source based on seller ID and API name
        $query = SellersApiImports::where('seller_id', $seller->id)
            ->where('api_name', $api_name);

        // Add shopurl filter if provided
        if ($shopurl) { $query->where('shopurl', $shopurl); }

        // Add api_token filter if provided
        if ($api_token) { $query->where('api_token', $api_token); }

        // Return the first matching lead source, or null if no match is found
        return $query->first();
    }

    /**
     * Delete LeadsSource Account
     *
     * @param array $options
     * @return array
     */
    public function deleteLeadsSourceAccount(array $options): array {
        // Extract seller ID and API name from options
        $apiName = $options['api_name'] ?? null;

        // Ensure seller ID is provided before proceeding with deletion
        if ($apiName) {
            try {
                SellersApiImports::where('seller_id', $this->currentSeller->id)
                    ->where('api_name', $apiName)
                    ->delete();

                // If API name is google-sheets, also delete related auto imports
                if ($apiName === 'google-sheets') {
                    SellersAutoImports::where('seller_id', $this->currentSeller->id)
                        ->where('api_type', 'google_sheets')
                        ->delete();
                }

                return [
                    'response' => 'success',
                    'message' => 'Lead source deleted successfully'
                ];
            } catch (\Exception $e) {
                return [
                    'response' => 'error',
                    'message' => 'Failed to delete lead source'
                ];
            }
        }

        return [
            'response' => 'error',
            'message' => 'API name is required'
        ];
    }

    /**
     * Creates or updates an auto-import record for a lead source.
     */
    public function createLeadsSourceAutoImport($options){
        // Extract options safely using null coalescing operator
        $spreadsheetId = $options['spreadsheetId'] ?? null;
        $sheetName = $options['sheetName'] ?? null;
        $apiType = $options['apiType'] ?? null;
        $lastIndice = $options['lastIndice'] ?? null;

        // Ensure required fields are present
        if (!$spreadsheetId || !$sheetName || !$apiType) {
            return null;
        }

        // Prepare data for insertion or update
        $dataImport = [
            'seller_id' => $this->currentSeller->id,
            'file_id' => $spreadsheetId,
            'file_name' => $sheetName,
            'seller_name' => $this->currentSeller->fullname . " (" . $this->currentSeller->name_page . ")",
            'api_type' => $apiType,
            'auto_import' => "yes",
            'type_googelsheet' => "default",
            'last_indice' => $lastIndice,
            'last_imported' => now(),
        ];

        // Create or update the auto-import record
        $rowAutoImport = $this->getOrCreateRowAutoImport($dataImport);

        // Ensure API token exists
        $this->ensureApiToken($rowAutoImport);

        return $rowAutoImport;
    }

    /**
     * get Or Create Auto Import
     */
    private function getOrCreateRowAutoImport(array $dataImport){
        $rowAutoImport = SellersAutoImports::where('seller_id', $dataImport['seller_id'])
            ->where('file_id', $dataImport['file_id'])
            ->where('file_name', $dataImport['file_name'])
            ->where('api_type', $dataImport['api_type'])
            ->first();

        if (!$rowAutoImport) {
            return SellersAutoImports::create($dataImport);
        }
        $rowAutoImport->update($dataImport);
        return $rowAutoImport;
    }

    /**
     * ensure Api Token
     */
    private function ensureApiToken($rowAutoImport){
        if (!$rowAutoImport->api_token) {
            $parentRow = SellersApiImports::where('seller_id', $rowAutoImport->seller_id)
                ->orderByDesc('id')
                ->first();

            if ($parentRow && $parentRow->api_token) {
                $rowAutoImport->update(['api_token' => $parentRow->api_token]);
            }
        }
    }
}