<?php

namespace App\Services\Validator;

use App\Services\Orders\FilterOrders;

class OrdersValidator{

    /**
     * Validate Product
     */
    public function validateOrder(array $searchData = [], array $options = []): array {
        // Check if ID exists
        if (!($searchData['id'] ?? null) && !($searchData['idOrderCode'] ?? null)) {
            return $this->errorResponse(__('orders.order_id_is_required'));
        }

        // Retrieve Product
        $rowOrder = $this->getOrder($searchData);
        if (!$rowOrder) {
            return $this->errorResponse(__('orders.order_not_found'));
        }

        return [
            'response' => 'success',
            'rowOrder' => $rowOrder,
        ];
    }

    /**
     * get order
     */
    private function getOrder(array $searchData){
        $searchData['first'] = 1;
        return (new FilterOrders())->getOrders($searchData);
    }

    /**
     * Generate a standardized error response.
     */
    private function errorResponse(string $message): array{
        return [
            'response' => 'error',
            'message' => $message,
        ];
    }
}