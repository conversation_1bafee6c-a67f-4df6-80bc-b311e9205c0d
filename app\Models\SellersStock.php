<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersStock extends Model
{
    //
     protected $table = 'sellers_stock';

	public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
	    'page_name',
	    'name',
	    'arabic_name',
	    'attribute_names',
	    'quantity',
	    'reference',
	    'reference_code',
	    'weight',
	    'width',
	    'height',
	    'length',
	    'statut',
	    'is_archive',
	    'description_callcenter',
	    'product_link',
	    'product_video',
	    'parent',
	    'total_products',
	    'warehouse_id',
	    'warehouse_name',
	    'shipping_price_type',
	    'shipping_by',
	    'confirmed_by',
	    'confirmed_at',
	    'created_by',
	    'updated_by',
	    'liste_stock',
	    'product_type',
	    'hscode',
	    'categorie_name',
	    'saleprice',
	    'is_test',
	    'statut_chatbot',
	    'type',
	    'declared_value',
	];

	protected $guarded = []; 

	/**
	 * sales Prices
	 */
	public function salesPrices(){
        return $this->hasMany(SellersStockOffers::class, 'product_id', 'id');
    }
	/**
	 * upsell
	 */
	public function upsell(){
        return $this->hasMany(SellersStockUpsells::class, 'product_id', 'id');
    }
}
    