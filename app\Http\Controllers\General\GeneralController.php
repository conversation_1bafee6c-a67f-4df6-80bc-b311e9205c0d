<?php

namespace App\Http\Controllers\General;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GeneralController extends Controller{
    /**
     * Retrieve and return a models of filtered models.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function models(Request $request): JsonResponse{
       // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => [
                'importOrdersFromExcel' => 'https://docs.google.com/spreadsheets/d/1rJ7O_Xn06e4R_yp5kKmp3iaKdpS7lHh1/edit?usp=sharing&ouid=108857075910547450301&rtpof=true&sd=true',
                'importOrdersFromGoogleSheet' => 'https://docs.google.com/spreadsheets/d/1jV1E5wVm7XRYav0CnM55KZY1O2DTW7GhBE5HJGSFTgk/edit?usp=sharing',
            ],
        ]);
    }

    /**
     * Retrieve and return available shipping methods.
     *
     * @return JsonResponse
     */
    public function shippingMethods(): JsonResponse {
        return response()->json([
            'response' => 'success',
            'result' => [
                'byair' => 'By Air',
                'bysea' => 'By Sea',
            ],
        ]);
    }

    /**
     * Process and return mode details.
     *
     * @return JsonResponse
     */
    public function processMode(): JsonResponse {
        return response()->json([
            'response' => 'success',
            'result' => [
                'Bulk' => 'Min Qty 100, Choose the destination Country',
                'One by one' => 'Min Qty 30, no destination country',
            ],
        ]);
    }
}