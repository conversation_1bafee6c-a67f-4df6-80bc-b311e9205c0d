<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\SendCodeResetPassword;
use App\Models\ResetCodePassword;
use App\Models\Sellers;
use App\Models\User;
use App\Services\Fields\FieldsService;
use App\Services\Notifications\MailService;
use App\Services\Sellers\FilterSellers;
use App\Services\TwoFactorAuth\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class OnboardingController extends Controller{
    protected FilterSellers $FilterSellers;
    protected TwoFactorAuthService $twoFactorAuthService;
    protected MailService $mailService;

    /**
     * Construct
     */
    public function __construct(TwoFactorAuthService $twoFactorAuthService){
        $this->FilterSellers = new FilterSellers();
        $this->twoFactorAuthService = $twoFactorAuthService;
        $this->mailService = new MailService();
    }



    /**
     * Forgot Password
     */
    public function forgotPassword(Request $request)
    {
        // Validate forgot password request
        $validateForgotPassword = $this->validateForgotPasswordRequest($request);
        if ($validateForgotPassword['response'] === 'error') {
            return response()->json($validateForgotPassword, 400);
        }

        // Find user by email
        $user = $this->findUserByEmail($request->email);
        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        // Generate and save reset code
        $codeData = $this->generateAndSaveResetCode($request->email);

        // Send reset code email
        $emailResult = $this->sendResetCodeEmail($request->email, $codeData->code);
        if ($emailResult['response'] === 'error') {
            return response()->json($emailResult, 500);
        }

        return response()->json([
            'response' => 'success',
            'message' => 'Password reset code has been sent to your email.',
        ], 200);
    }


    public function checkResetCode(Request $request)
    {
        // Validate check reset code request
        $validateCheckCode = $this->validateCheckResetCodeRequest($request);
        if ($validateCheckCode['response'] === 'error') {
            return response()->json($validateCheckCode, 422);
        }

        // Find and validate reset code
        $resetCodeResult = $this->findAndValidateResetCode($request->code, $request->email);
        if ($resetCodeResult['response'] === 'error') {
            return response()->json($resetCodeResult, 422);
        }

        // Return success response
        return response()->json(['message' => 'Reset code is valid.'], 200);
    }

    public function resetPassword(Request $request)
    {
        // Validate reset password request
        $validateResetPassword = $this->validateResetPasswordRequest($request);
        if ($validateResetPassword['response'] === 'error') {
            return response()->json($validateResetPassword, 422);
        }

        // Find and validate reset code
        $resetCodeResult = $this->findAndValidateResetCode($request->code, $request->email);
        if ($resetCodeResult['response'] === 'error') {
            return response()->json($resetCodeResult, 422);
        }

        // Update user password
        $updateResult = $this->updateUserPassword($resetCodeResult['passwordReset'], $request->password);
        if ($updateResult['response'] === 'error') {
            return response()->json($updateResult, 404);
        }

        // Delete the used reset code
        $resetCodeResult['passwordReset']->delete();

        return response()->json(['message' => 'Password has been successfully reset.'], 200);
    }



    /**
     * Validate forgot password request
     */
    private function validateForgotPasswordRequest(Request $request){
        try {
            $request->validate([
                'email' => 'required|email',
            ]);
            return ['response' => 'success'];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ];
        }
    }

    /**
     * Find user by email
     */
    private function findUserByEmail($email){
        return User::firstWhere('email', $email);
    }

    /**
     * Generate and save reset code
     */
    private function generateAndSaveResetCode($email){
        // Delete all old codes that the user sent before
        ResetCodePassword::where('email', $email)->delete();

        // Generate random code
        $data = [
            'email' => $email,
            'code' => mt_rand(100000, 999999)
        ];

        // Create a new code
        return ResetCodePassword::create($data);
    }

    /**
     * Send reset code email
     */
    private function sendResetCodeEmail($email, $code){
        try {
            $seller = Sellers::where('email', $email)->first();

            // Prepare email content
            $emailData = [
                'toEmail' => $email,
                'subject' => 'Password Reset Code',
                'body' => "Hello {$seller->fullname},<br><br>Here is your password reset code: {$code}<br><br>Best regards,<br>The Power Group Company Team",
            ];
            $this->mailService->SendMail($emailData);

            return ['response' => 'success'];
        } catch (\Exception $e) {
            return [
                'response' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Validate check reset code request
     */
    private function validateCheckResetCodeRequest(Request $request){
        try {
            $request->validate([
                'code' => 'required|string|exists:reset_code_passwords',
                'email' => 'required|email',
            ]);
            return ['response' => 'success'];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ];
        }
    }

    /**
     * Validate reset password request
     */
    private function validateResetPasswordRequest(Request $request){
        try {
            $request->validate([
                'code' => 'required|string|exists:reset_code_passwords',
                'email' => 'required|email',
                'password' => 'required|string|min:6',
            ]);
            return ['response' => 'success'];
        } catch (\Illuminate\Validation\ValidationException $e) {
            return [
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ];
        }
    }

    /**
     * Find and validate reset code
     */
    private function findAndValidateResetCode($code, $email){
        // Find the reset code entry
        $passwordReset = ResetCodePassword::where('code', $code)
            ->where('email', $email)
            ->first();

        if (!$passwordReset) {
            return [
                'response' => 'error',
                'message' => 'Invalid reset code.'
            ];
        }

        // Check expiration (1 hour)
        if ($passwordReset->created_at->addHour()->isPast()) {
            $passwordReset->delete();
            return [
                'response' => 'error',
                'message' => 'Reset code has expired.'
            ];
        }

        return [
            'response' => 'success',
            'passwordReset' => $passwordReset
        ];
    }

    /**
     * Update user password
     */
    private function updateUserPassword($passwordReset, $newPassword){
        // Find user by email
        $user = User::firstWhere('email', $passwordReset->email);

        if (!$user) {
            return [
                'response' => 'error',
                'message' => 'User not found.'
            ];
        }

        // Update password
        $user->password = bcrypt($newPassword);
        $user->save();

        return ['response' => 'success'];
    }

    /**
     * render User Infos
     */
    private function renderUserInfos($user){
        // Retrieve the seller record
        return $this->FilterSellers->getSellers([
            "user_id" => $user->id,
            "first" => 1,
        ]);
    }
}
