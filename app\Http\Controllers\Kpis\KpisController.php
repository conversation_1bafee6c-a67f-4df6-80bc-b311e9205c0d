<?php

namespace App\Http\Controllers\Kpis;

use App\Http\Controllers\Controller;
use App\Services\Kpis\KpisConfirmation;
use App\Services\Kpis\KpisFollowup;
use App\Services\Kpis\KpisShipping;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class KpisController extends Controller{
    protected KpisConfirmation $KpisConfirmation;
    protected KpisShipping $KpisShipping;
    protected KpisFollowup $KpisFollowup;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->KpisConfirmation = new KpisConfirmation();
        $this->KpisShipping = new KpisShipping();
        $this->KpisFollowup = new KpisFollowup();
    }

    /**
     * Confirmations Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmation(Request $request): JsonResponse{
        $confirmationKpis = $this->KpisConfirmation->confirmationKpis($request->all());
        
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $confirmationKpis,
        ]);
    }

    /**
     * Shipping Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function shipping(Request $request): JsonResponse{
        $shippingKpis = $this->KpisShipping->shippingKpis($request->all());
        
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $shippingKpis,
        ]);
    }

    /**
     * Followup Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function followup(Request $request): JsonResponse{
        $followupKpis = $this->KpisFollowup->followupKpis($request->all());
        
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $followupKpis,
        ]);
    }
}