<?php

namespace App\Services\Validator;

use App\Models\SellersStock;
use App\Models\SellersStockOffers;
use App\Models\SellersStockUpsells;
use App\Services\Warehousing\FilterProducts;

class ProductsValidator{

    /**
     * Validate Product
     */
    public function validateProduct(array $searchData = [], array $options = [],$checkVariable = false): array {
        // Check if ID exists
        if (empty($searchData['id'])) {
            return $this->errorResponse(__('products.product_id_is_required'));
        }

        // Retrieve Product
        $rowProduct = $this->getProduct($searchData);
        if (!$rowProduct) {
            return $this->errorResponse(__('products.product_not_found'));
        }
        if($checkVariable && $rowProduct->productType === 'simple')
        {
            return $this->errorResponse(__('products.product_is_simple'));
        }
        return [
            'response' => 'success',
            'rowProduct' => $rowProduct,
        ];
    }

    /**
     * Validate a product upsell.
     *
     * @param int $productId
     * @param int $upsellId
     * @return array|JsonResponse
     */
    public function validateUpsell($productId, $upsellId)
    {
        // Check if the product has the specified upsell
        $existingUpsell = SellersStockUpsells::where('product_id', $productId)
            ->where('id', $upsellId)
            ->first();

        if (!$existingUpsell) {
            return [
                'response' => 'error',
                'message' => __('products.upsell_not_found'),
            ];
        }

        // Return the upsell if it exists
        return [
            'response' => 'success',
            'upsell' => $existingUpsell,
        ];
    }

    /**
     * Validate a product upsell.
     *
     * @param int $productId
     * @param int $offerId
     * @return array|JsonResponse
     */
    public function validateOffer($productId, $offerId)
    {
        // Check if the product has the specified upsell
        $existingOffer = SellersStockOffers::where('product_id', $productId)
            ->where('id', $offerId)
            ->first();

        if (!$existingOffer) {
            return [
                'response' => 'error',
                'message' => __('products.offer_not_found'),
            ];
        }

        // Return the upsell if it exists
        return [
            'response' => 'success',
            'offer' => $existingOffer,
        ];
    }
    /**
     * Validate a product variant.
     *
     * @param int $productId
     * @param int $variantId
     * @return array|JsonResponse
     */
    public function validateVariant($productId, $variantId)
    {
        // Check if the product has the specified upsell
        $existingVariant = SellersStock::where('id', $variantId)
            ->where('parent', $productId)
            ->first();

        if (!$existingVariant) {
            return [
                'response' => 'error',
                'message' => __('products.variant_not_found'),
            ];
        }

        // Return the upsell if it exists
        return [
            'response' => 'success',
            'variant' => $existingVariant,
        ];
    }

    /**
     * Retrieve product details based on search criteria.
     */
    private function getProduct(array $searchData){
        $searchData['first'] = 1;
        return (new FilterProducts())->getProducts($searchData);
    }

    /**
     * Generate a standardized error response.
     */
    private function errorResponse(string $message): array{
        return [
            'response' => 'error',
            'message' => $message,
        ];
    }
}