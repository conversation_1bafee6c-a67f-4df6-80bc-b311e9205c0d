<?php

namespace App\Services\TwoFactorAuth;

use App\Models\Sellers;
use App\Models\User;
use Illuminate\Http\Request;
use PragmaRX\Google2FA\Google2FA;

class TwoFactorAuthService
{
    protected $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * Generate a new secret key for the user
     *
     * @param Sellers $seller
     * @return string
     */
    public function generateSecretKey(Sellers $seller): string
    {

        $secretKey = $this->google2fa->generateSecretKey();

        $seller->two_factor_enabled = true;
        // Store the secret key in the database
        $seller->two_factor_secret = $secretKey;
        $seller->save();
        return $secretKey;
    }

    /**
     * Generate a QR code URL for the user to scan with Google Authenticator
     *
     * @param Sellers $seller
     * @param string $secretKey
     * @return string
     */
    public function getQRCodeUrl(Sellers $seller, string $secretKey): string
    {
        $userEmail = $seller->email ?? $seller->email_pro ?? "seller-{$seller->id}";

        return $this->google2fa->getQRCodeUrl(
            'Power Group World',
            $userEmail,
            $secretKey
        );
    }

    /**
     * Verify the provided OTP code
     *
     * @param string $code
     * @param Sellers $seller
     * @return array
     */
    public function verifyCode(string $code, Sellers $seller)
    {
        if (empty($seller->two_factor_secret)) {
            return [
                'response' => 'error',
                'message' => '2FA is not enabled for this user',
            ];
        }
        if($this->google2fa->verifyKey($seller->two_factor_secret, $code)){
            return [
                    'response' => 'success',
                    'message' => 'Code verified successfully',
            ];
        }else{
            return [
                'response' => 'error',
                'message' => 'Invalid verification code',
            ];
        }
    }

    /**
     * Enable 2FA for a user
     *
     * @param Sellers $seller
     * @return void
     */
    public function enable2FA(Sellers $seller): void
    {
        $seller->two_factor_enabled = true;
        $seller->save();
    }

    /**
     * Disable 2FA for a user
     *
     * @param Sellers $seller
     * @return void
     */
    public function disable2FA(Sellers $seller): void
    {
        $seller->two_factor_enabled = false;
        $seller->two_factor_secret = null;
        $seller->save();
    }

    /**
     * Check if 2FA is enabled for a user
     *
     * @param Sellers $seller
     * @return bool
     */
    public function is2FAEnabled(Sellers $seller): bool
    {
        return $seller->two_factor_enabled;
    }
}