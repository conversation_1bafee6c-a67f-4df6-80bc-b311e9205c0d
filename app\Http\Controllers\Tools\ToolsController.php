<?php

namespace App\Http\Controllers\Tools;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use GlobalHelper;

class ToolsController extends Controller{
    /**
     * Simulate FIS (Fulfillment, Inventory, and Shipping) calculations based on input parameters.
     *
     * @param Request $request The HTTP request containing simulation parameters
     * @return JsonResponse A JSON response with simulation results or validation errors
     */
    public function simulator(Request $request){
        // Validate the request
        $validationResult = $this->validateSimulatorRequest($request);
        if (isset($validationResult['error'])) {
            return $validationResult['error'];
        }
        $validatedData = $validationResult['data'];

        // Calculate simulation results
        $result = $this->calculateSimulationResults($validatedData);

        // Format the results
        $formattedResult = $this->formatSimulationResults($result);

        // Return a success response
        return response()->json([
            'response' => 'success',
            'result' => $formattedResult,
        ]);
    }

    /**
     * Validate the simulator request data
     *
     * @param Request $request The HTTP request containing simulation parameters
     * @return array Validation result with data or error
     */
    private function validateSimulatorRequest(Request $request){
        $rules = [
            'leads' => 'required|numeric',
            'cDomestic' => 'required|numeric',
            'dDomestic' => 'required|numeric',
            'sellingPrice' => 'required|numeric',
            'productCost' => 'required|numeric',
            'gWeight' => 'required|numeric',
            'vWeight' => 'required|numeric',
            'length' => 'required|numeric',
            'height' => 'required|numeric',
            'width' => 'required|numeric',
            'costLead' => 'required|numeric',
            'confirmationFees' => 'required|numeric',
            'deliveredFees' => 'required|numeric',
            'shippingFees' => 'required|numeric',
            'codFees' => 'required|numeric',
            'fullfillmentFees' => 'required|numeric',
            'returnFees' => 'required|numeric',
            'codFeesType' => 'required|string',
            'enteredLead' => 'required|string',
            'shippingType' => 'required|string',

        ];

        $validator = Validator::make($request->all(), $rules);

        // Check if the validation fails
        if ($validator->fails()) {
            return [
                'error' => response()->json([
                    'response' => 'error',
                    'errors' => $validator->errors(),
                ], 422)
            ];
        }

        // Return the validated data
        return ['data' => $validator->validated()];
    }

    /**
     * Calculate simulation results based on validated data
     *
     * @param array $validatedData The validated input data
     * @return array Calculation results
     */
    private function calculateSimulationResults(array $validatedData){

        $C = floor($validatedData['leads'] * $validatedData['cDomestic']);
        $D = floor($C * $validatedData['dDomestic']);

        $confirmationFees = $validatedData['confirmationFees'];
        $deliveredFees = $validatedData['deliveredFees'];
        $shippingFees = $validatedData['shippingFees'];
        $codFees = $validatedData['codFees'];
        $fulfillmentFees = $validatedData['fullfillmentFees'];
        $returnFees = $validatedData['returnFees'];
        $enteredLead = $validatedData['enteredLead'];
        $shippingType = $validatedData['shippingType'];

        // Calculate CPD Calls
        $cpd_calls = $D > 0 ? ($C * $confirmationFees + $D * $deliveredFees) / $D : 0;
        $result = [];

        // Process options
        $result['Leads'] = $validatedData['leads'];
        $result['SH/U'] = $shippingFees;
        $result['Confirmed'] = $C;
        $result['Delivered'] = $D;

        $result['Sales'] = $validatedData['sellingPrice'] * $D;
        $result['Products'] = $validatedData['productCost'] * $D;
        $result['Fulfillment'] = $C * $fulfillmentFees;
        $result['Shipping'] = $shippingType === 'shipped' ? $C * $shippingFees : $D * $shippingFees;
        // Use custom shipping if provided, otherwise use country-based shipping
        // if(isset($custom_shipping)) {
        //     $result['Shipping'] = $C * $custom_shipping;
        // } else {
        //     $result['Shipping'] = $C * $shippingFees;
        // }


        $result['VAT'] = 0;
        $result['COD'] = $validatedData['codFeesType'] == 'percentage' ? $validatedData['sellingPrice'] * ($codFees/ 100) * $D  : $codFees * $D;
        $result['Returns'] = ($C - $D) * $returnFees;
        $result['Ads'] = $validatedData['leads'] * $validatedData['costLead'];
        $result['Calls']  = $D * $cpd_calls;

        $result['CPD Calls'] = $cpd_calls;
        $result['CPD Product'] = ($D > 0) ? $result['Products'] / $D : 0;
        if ($shippingType === 'shipped') {
            $result['CPD Shipping'] = ($D > 0) ? $result['Shipping'] / $D : 0;
        } else {
            $result['CPD Shipping'] = ($C > 0) ? ($result['Shipping'] + $result['Returns']) / $C : 0; // shipping plus return / confirmed
        }
        $result['CPD Fulfillment'] = ($D > 0) ? $result['Fulfillment'] / $D : 0;
        $result['CPD COD'] = ($D > 0) ? $result['COD'] / $D : 0;
        $result['CPD Returns'] = ($D > 0) ? $result['Returns'] / $D : 0;
        $result['CPD Ads'] = ($D > 0) ? $result['Ads'] / $D : 0;
        $result['CPD VAT'] =  ($D > 0) ? $result['VAT'] / $D : 0;

        $result['CPD Entered Lead'] = ($D > 0) ? ($enteredLead * $validatedData['leads']) / $D : 0;

        $result['CPD'] = $result['CPD Entered Lead']  + $result['CPD Calls'] + $result['CPD Product'] + $result['CPD Shipping'] + $result['CPD Fulfillment'] + $result['CPD COD'] + $result['CPD Returns'] + $result['CPD Ads'] + $result['CPD VAT'];
        $result['EPD'] = $validatedData['sellingPrice'] - $result['CPD'];

        return $result;
    }

    /**
     * Format the simulation results
     * @param array $result The calculation results
     * @return array Formatted results
     */
    private function formatSimulationResults(array $result){
        $formattedResult = [];
        foreach ($result as $key => $value) {
            $formattedResult[$key] = is_numeric($value) ? GlobalHelper::formatNumber($value) : $value;
        }
        return $formattedResult;
    }
}