<?php

namespace App\Services\LeadSource;
use Google\Client;
use Google\Service\Sheets;
use Google\Service\Drive;


use SellersHelper;

class GoogleSheetsService{

    /**
     * constructor.
     */
    protected $currentSeller;
    protected LeadSourceService $LeadSourceService;

    public function __construct(){

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();

        // Instant Services
        $this->LeadSourceService = new LeadSourceService();
    }

    /**
     * Create and return a configured Google Client instance.
     *
     * @return Client
     * @throws \Exception If the credentials file is missing.
     */
    public function createGoogleClient($params = []): Client{
        $companyName = env('COMPANY_NAME', 'DefaultCompany');
        $credentialsPath = public_path('library/google-sheets/credentials.json');

        // Create a new Google Client instance
        $client = new Client();
        $client->setApplicationName($companyName);
        $client->setScopes(scope_or_scopes: [
            Sheets::SPREADSHEETS_READONLY,
            Drive::DRIVE_METADATA_READONLY
        ]);
        $client->setAuthConfig($credentialsPath);
        $client->setAccessType('offline');
        $client->setState(json_encode(["seller_id" => $this->currentSeller->id]));
        $client->setPrompt('select_account consent');
        $client->setRedirectUri(route('googlesheets.oauth2callback'));

        return $client;
    }

    /**
     * Load and authenticate a Google Client instance.
     *
     * @param string|null $accessToken The access token for authentication.
     * @return array Status and the authenticated Google Client instance or authorization URL.
     */
    public function loadGoogleClient(?string $accessToken = ""): array{
        try {
            // Create a new Google Client instance
            $client = $this->createGoogleClient();

            // Retrieve the token if not provided
            if (!$accessToken) {
                $accessToken = $this->LeadSourceService->getLeadSourceToken("google-sheets");
            }

            // Set access token if available
            if ($accessToken) {
                $client->setAccessToken($accessToken);
            }

            // Check if the access token is expired
            if ($client->isAccessTokenExpired()) {
                // Check if the client has a refresh token
                if (!$client->getRefreshToken() || isset($client->fetchAccessTokenWithRefreshToken($client->getRefreshToken())['error'])) {
                    // Either no refresh token or error in access token, request user authorization
                    return [
                        'response' => 'success',
                        'actionType' => 'redirect',
                        'url' => $client->createAuthUrl(),
                    ];
                }
            }

            return [
                'response' => 'success',
                'client' => $client,
            ];
        } catch (\Exception $e) {
            // Error handling
            return [
                'response' => 'error',
                'message' => 'Error loading google client: ' . $e->getMessage(),
            ];
        }
    }
}