<?php

namespace App\Http\Controllers\Warehousing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\SellersStockUpsells;
use App\Models\SellersStockUpsellsPrices;
use App\Services\Fields\FieldsService;
use App\Services\Validator\ProductsValidator;
use SellersHelper;

class ProductsUpsellController extends Controller{
    protected ProductsValidator $productsValidator;
    protected FieldsService $FieldsService;
    protected ProductsController $productsController;
    protected $currentSeller;

    public function __construct(){
        $this->productsValidator = new ProductsValidator();
        $this->FieldsService = new FieldsService();
        $this->productsController = new ProductsController();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }


    /**
     * Create a new product upsell.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request, $productId): JsonResponse
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'productsUpsell',
            'option' => 'createValidation',
            'data' => $request->all(),

        ]);

        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'store',
            'type' => 'productsUpsell',
            'data' => $request->all(),
            'AppendParams' => [
                'seller_id' =>  $this->currentSeller->id,
                'seller_name' =>  $this->currentSeller->fullname,
                'product_name' => $validateProduct['rowProduct']->name,
                'product_id' => $productId,
            ]

        ]);

        // Create the upsell
        $upsell = SellersStockUpsells::create($processData);

        // add Upsell Prices
        if ($request->has('prices')) {
            $this->addUpsellPrices($request->prices, $productId, $validateProduct['rowProduct']->name, $upsell);
        }

        //get product details
        $productDetails = $this->productDetails($request,$productId);

        // Return the response with the new upsell and its prices
        return response()->json([
            'response' => 'success',
            'message' => __('products.upsell_created_successfully'),
            'result' => $productDetails,
        ], 201);
    }


    /**
     * update a product upsell.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request, $productId, $upsellId)
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate the upsell
        $validationResult = $this->productsValidator->validateUpsell($productId, $upsellId);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 404);
        }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'productsUpsell',
            'option' => 'updateValidation',
            'data' => $request->all(),
        ]);
        if ($resultValidator['response'] == "error") {
            return response()->json($resultValidator, 400);
        }

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'update',
            'type' => 'productsUpsell',
            'data' => $request->all(),
        ]);

        // Update the upsell
        $upsell = $validationResult['upsell'];
        $upsell->update($processData);

        // add Upsell Prices
        if ($request->has('prices')) {
            $this->addUpsellPrices($request->prices, $productId, $validateProduct['rowProduct']->name, $upsell, true);
        }

        //get product details
        $productDetails = $this->productDetails($request,$productId);

        // Return the response with the updated upsell and its prices
        return response()->json([
            'response' => 'success',
            'message' => __('products.upsell_updated_successfully'),
            'result' => $productDetails,
        ]);

    }

    /**
     * Delete a product upsell.
     *
     * @param int $productId
     * @param int $upsellId
     * @return JsonResponse
     */
    public function destroy($productId, $upsellId): JsonResponse
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId, 'layout' => 'details', "with" => ["salesPrices.prices", "upsell.prices"]]);
        if ($validateProduct['response'] == 'error') {
            return response()->json($validateProduct, 404);
        }

        // Validate the upsell
        $validationResult = $this->productsValidator->validateUpsell($productId, $upsellId);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 404);
        }

        // Delete the upsell prices
        SellersStockUpsellsPrices::where('upsell_id', $upsellId)->delete();

        // Delete the upsell
        $validationResult['upsell']->delete();

        // Return the response
        return response()->json([
            'response' => 'success',
            'message' => __('products.upsell_deleted_successfully'),
        ]);
    }


    /**
     * Add prices for a product upsell.
     *
     * @param array $prices
     * @param int $productId
     * @param string $productName
     * @param SellersStockUpsells $upsell
     * @param bool $deleteBefore
     * @return void
     */
    private function addUpsellPrices(array $prices, int $productId, string $productName, SellersStockUpsells $upsell, bool $deleteBefore = false)
    {
        if ($deleteBefore) {
            // Delete existing prices for the upsell
            SellersStockUpsellsPrices::where('upsell_id', $upsell->id)->delete();
        }

        foreach ($prices as $price) {
            SellersStockUpsellsPrices::create([
                'product_id' => $productId,
                'product_name' => $productName,
                'upsell_id' => $upsell->id,
                'upsell_name' => $upsell->upsell_name,
                'currency' => $price['currency'],
                'price' => $price['price'],
            ]);
        }
    }

    /**
     * Get product details from products controller
     */
    private function productDetails($request,$id){
        // Call the details method (for product)
        $detailsResponse = $this->productsController->details($request, $id);

        // Extract the data from the JsonResponse object
        return $detailsResponse->getData(true)['result'];
    }


}