<?php
namespace App\Services\Sourcing;

use App\Models\SourcingInvoices;
use App\Services\Fields\FieldsService;
use App\Services\Fields\FormatterInvoicesSourcing;
use App\Traits\FilterQuery;
use SellersHelper;

class FilterSourcingInvoices {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $FormatterInvoicesSourcing;
    protected $currentSeller;
    protected $columnsType;
    use FilterQuery;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->FormatterInvoicesSourcing = new FormatterInvoicesSourcing();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();

        // Table name
        $this->baseTable = 'sourcing_invoices';
        $this->columnsType = 'sourcingInvoices';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getInvoices(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // General Conditions
        $this->applyGeneralFilters();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();

        // Fetch and return the final filtered results
        return $this->fetchResults($this->FormatterInvoicesSourcing);
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = SourcingInvoices::where("{$this->baseTable}.statut_validate", 'open')
                                        ->where("{$this->baseTable}.seller_id", $this->currentSeller->id)
                                        ->where("{$this->baseTable}.statut",'published');
    }

    /**
     * Apply general filters to the query.
     */
    private function applyGeneralFilters(){
        // Get the parent filter value from data, default to null if not set
        $invoiceNum = $this->data["invoiceNum"] ?? null;
        $startDate = $this->data['startDate'] ?? null;
        $endDate = $this->data['endDate'] ?? null;

        // Filter By Num
        if($invoiceNum){ $this->query->where("{$this->baseTable}.invoice_num", "like","%".$invoiceNum."%"); }

        // Apply date filters
        if ($startDate) {
            $this->query->whereDate("{$this->baseTable}.date", ">=", $startDate);
        }
        if ($endDate) {
            $this->query->whereDate("{$this->baseTable}.date", "<=", $endDate);
        }
    }

    /**
     * Format an invoice using sourcingInvoices.json fields.
     *
     * @param SourcingInvoices $invoice
     * @return array
     */
    public function formatInvoice(SourcingInvoices $invoice): array
    {
        // Use FieldsService to format the invoice based on sourcingInvoices.json
        return $this->FieldsService->formatData([
            'type' => 'sourcingInvoices',
            'data' => $invoice->toArray(),
        ]);
    }
}