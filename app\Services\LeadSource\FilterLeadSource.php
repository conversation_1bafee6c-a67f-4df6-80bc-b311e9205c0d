<?php
namespace App\Services\LeadSource;

use App\Models\SellersApiImports;
use App\Models\SellersAutoImports;
use App\Services\Fields\FieldsService;
use <PERSON><PERSON><PERSON>elper as GlobalSellersHelper;

class FilterLeadSource {
    protected $data;
    protected $firstQuery;
    protected $secondeQuery;
    protected $FieldsService;
    protected $columnsType;
    protected $secondeColumnsType;
    protected $currentSeller;
    protected ShopifyService $shopifyService;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->shopifyService = new ShopifyService();

        // Table name
        $this->columnsType = 'leadsourcesLines';
        $this->secondeColumnsType = 'leadsources';

        // Current Seller
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
    }


    /**
     * Get filtered orders based on input data.
     */
    public function getLeadSources(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // Fetch and return the final filtered results
        return $this->fetchResults();
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial First Query
        $this->firstQuery = SellersAutoImports::orderBy('id','desc')->where("seller_id", $this->currentSeller->id);

        // Initial Seconde Query
        $this->secondeQuery = SellersApiImports::orderBy('id','desc')
                                ->whereNotIn('api_name',['google-sheets'])
                                ->whereNotNull('api_token')
                                ->where("seller_id", $this->currentSeller->id);


    }

    /**
     * Apply custom column selection if provided, otherwise use default columns.
     */
    private function applyCustomColumns(): void {
        $customColumns = $this->data['custom_columns'] ?? [];

        if (!empty($customColumns)) {
            $this->firstQuery->select($customColumns);
            $this->secondeQuery->select($customColumns);
        } else {
            // First Query
            $firstData = $this->FieldsService->DefaultColumns($this->columnsType, $this->data);
            $this->firstQuery->select($firstData['columns']);

            // Seconde Query
            $secondeData = $this->FieldsService->DefaultColumns($this->secondeColumnsType, $this->data);
            $this->secondeQuery->select($secondeData['columns']);
        }
    }

    /**
     * Fetch results based on pagination and other parameters.
     */
    private function fetchResults() {
        $perPage = $this->data['perPage'] ?? env('PER_PAGE', 15);
        $noPaginate = $this->data['noPaginate'] ?? false;
        $first = $this->data['first'] ?? false;
        $noFormatter = $this->data['noFormatter'] ?? false;

        // Otherwise, fetch results with or without pagination
        $unionQuery = $this->firstQuery->union($this->secondeQuery);
        $results = $noPaginate ? $unionQuery->get() : $unionQuery->paginate($perPage);

        // Return results, optionally formatting them using FieldsHelper
        return $noFormatter ? $results : $this->FieldsService->ResponseResults([
            'results' => $results,
            'first' => $first,
            'noPaginate' => $noPaginate,
            'layout' => $this->data['layout'] ?? null,
            'type' => $this->columnsType,
        ]);
    }

    public function deleteLeadSourceImport(int $importId, string $apiType): array {

        if ($apiType === 'google_sheets') {
            return $this->deleteGoogleSheetImport($importId);

        }
        return $this->deleteRegularApiImport($importId,$apiType , true);
    }

    private function deleteGoogleSheetImport(int $importId): array {
        // Get the import to be deleted from auto_imports
        $import = SellersAutoImports::where('seller_id', $this->currentSeller->id)
            ->where('api_type', 'google_sheets')
            ->where('id', $importId)
            ->first();


        if (!$import) {
            return [
                'success' => false,
                'message' => 'Import not found'
            ];
        }


        // Delete the specific sheet import
        $import->delete();

        // Check remaining imports with the same access token
        $remainingImports = SellersAutoImports::where('seller_id', $this->currentSeller->id)
            ->where('api_type', 'google_sheets')
            ->count();

        // If no more imports exist with this token, delete the Google account
        if ($remainingImports === 0) {
            SellersApiImports::where('seller_id', $this->currentSeller->id)
                ->where('api_name', 'google-sheets')
                ->delete();

            return [
                'success' => true,
                'message' => 'Sheet import deleted and Google account disconnected',
                'account_deleted' => true
            ];
        }

        return [
            'success' => true,
            'message' => 'Sheet import deleted successfully',
            'account_deleted' => false
        ];
    }

    private function deleteRegularApiImport(int $importId,$apiType , $withUninstall = false): array {
        // Get the API import to be deleted
        $import = SellersApiImports::where('seller_id', $this->currentSeller->id)
            ->where('id', $importId)
            ->where('api_name', $apiType)
            ->first();

        if (!$import) {
            return [
                'success' => false,
                'message' => 'Import not found'
            ];
        }

        // Get the API import record before deletion for Shopify
        if ($apiType === 'shopify' && $withUninstall) {

            if ($import) {
                $uninstalled = $this->shopifyService->uninstallApp(
                    $import->shopurl,
                    $import->api_token
                );

            }
        }

        // Delete the import
        $import->delete();

        return [
            'success' => true,
            'message' => 'Integration disconnected successfully',
            'account_deleted' => true
        ];
    }

    /**
     * Extract access token from JSON-encoded token string
     *
     * @param string $tokenJson
     * @return string|null
     */
    private function extractAccessToken(?string $tokenJson): ?string
    {
        if (!$tokenJson) {
            return null;
        }

        try {
            $tokenData = json_decode($tokenJson, true);
            return $tokenData['access_token'] ?? null;
        } catch (\Exception $e) {
            // If it's not JSON or doesn't have access_token, return the original string
            return $tokenJson;
        }
    }

}