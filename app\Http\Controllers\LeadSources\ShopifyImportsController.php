<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\ShopifyService;
use App\Services\Orders\OrderAPIService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SellersHelper as GlobalSellersHelper;

class ShopifyImportsController extends Controller
{
    protected $shopifyService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->shopifyService = new ShopifyService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }


    /**
     * Install Shopify App - Redirect to authorization page.
     */

     public function install(Request $request)
     {
         // Validate request parameters
         $validationResponse = $this->shopifyService->validateRequestParameters($request, ['shop']);
         $data = $validationResponse->getData(true);

         if ($data['response'] === 'error') {
             return $validationResponse; // Return error response if validation failed
         }
         $shop = $data['result']['shop'];

        $redirectUri = env('SHOPIFY_REDIRECT_URI');
        $scopes = "read_orders,write_orders,read_products";
        $installUrl = "https://{$shop}/admin/oauth/authorize?"
            . "client_id=" . env('SHOPIFY_API_KEY')
            . "&scope={$scopes}"
            . "&redirect_uri=" . urlencode($redirectUri);

        // Decide response type:
        if ($request->wantsJson() || $request->query('json') == 1) {
            // Called from ERP React app — return JSON
            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ]);
        } else {
            // Called by Shopify admin (browser) — redirect immediately
            return redirect($installUrl);
        }
     }


    /**
     * Handle OAuth Callback - Get Access Token and Register Webhooks
     */
    public function callback(Request $request)
    {
        // Validate request parameters
        $validationResponse = $this->shopifyService->validateRequestParameters($request, ['shop', 'code']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return error response if validation failed
        }

        // Proceed with the validated parameters
        $shop = $data['result']['shop'];
        $code = $data['result']['code'];
        // Exchange code for access token
        $accessTokenResponse = $this->shopifyService->getAccessToken($shop, $code);


        // Decode the JSON response
        $accessTokenData = $accessTokenResponse->getData(true);

        // Check if an error occurred
        if ($accessTokenData['response'] === 'error') {
            return $accessTokenResponse; // Return the error response
        }

        $baseUrl =env('URL_APP_SELLERS').'/lead-sources';

        $redirectUrl = $baseUrl . '?name=shopify&shop=' . urlencode($shop) . '&token=' . urlencode($accessTokenData['result']);

        return redirect()->away($redirectUrl);
    }


    /**
     * Add a Shopify lead source for the current seller.
     *
     * This method validates the request parameters, retrieves the seller,
     * creates a lead source token, and registers Shopify webhooks.
     *
     * @param \Illuminate\Http\Request $request The incoming HTTP request containing the required parameters.
     *
     * @return \Illuminate\Http\JsonResponse A JSON response indicating success or failure.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException If the seller is not found.
     *
     * Request Parameters:
     * - token: The Shopify API token.
     * - shop: The Shopify shop URL.
     *
     * Response:
     * - On success: Returns a JSON response with a success message and HTTP status 200.
     * - On validation failure: Returns a JSON response with an error message and HTTP status 400.
     * - If seller is not found: Returns a JSON response with an error message and HTTP status 404.
     */
    public function addShopifySourceLead(Request $request)
    {
        // Validate required form data parameters 'token' and 'shop'
        $request->validate([
            'token' => 'required|string',
            'shop'  => 'required|string',
        ]);

        // Get validated inputs
        $token = $request->input('token');
        $shop = $request->input('shop');

        // Get seller
        $seller = Sellers::find($this->currentSeller->id);
        if (!$seller) {
            return response()->json([
                'response' => 'error',
                'message' => 'Seller not found',
            ], 404);
        }

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'shopify',
            'api_token' => $token,
            'seller' => $seller,
            'api_label' => $shop,
            'shopurl' => $shop,
        ]);

        // Register webhooks
        $this->registerWebhooks($shop, $token, $seller->id);

        return response()->json([
            'response' => 'success',
            'message' => 'Shopify lead source added successfully and webhooks registered.',
        ], 200);
    }

    /**
     * Register webhooks for the given topics.
     *
     * @param string $shop
     * @param string $accessToken
     * @param string $sellerId
     * @return void
     */
    protected function registerWebhooks($shop, $accessToken, $sellerId)
    {
        // Define all webhook topics
        $webhookTopics = [
            'orders/create',
            'orders/update',
        ];
        foreach ($webhookTopics as $topic) {
            $webhookResponse = Http::withHeaders([
                'X-Shopify-Access-Token' => $accessToken
            ])->post("https://{$shop}/admin/api/2025-01/webhooks.json", [
                'webhook' => [
                    'topic' => $topic,
                    'address' => env('SHOPIFY_APP_URL') . "shopify/" . $topic.'/'.$sellerId,
                    'format' => 'json'
                ]
            ]);

         $webhookData = $webhookResponse->json();

            if (!isset($webhookData['webhook'])) {
                Log::error("Failed to register webhook for {$topic}", $webhookData);
            }
        }
    }

     /**
     * Handle Order Webhook
     */
    public function handleOrderWebhook(Request $request,$id)
    {

        $request_from_shopify = $this->shopifyService->validateShopifyRequest($request);
        // save it same why as excel order
        if ($request_from_shopify !== true) {


            return $request_from_shopify; // HACKER possibility here
        }
        $storeDomain = $request->header('X-Shopify-Shop-Domain');

        // Formatter Data
        $orderData = $this->shopifyService->formatWebHookOrder([
            'store_name' => $storeDomain,
            ...$request->all()
        ]);
        // Send Order via api
        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $orderData,
            'sellerId' => $id
        ]);
 // Check if the response contains an error
 if ($responseOrders['response'] === 'error') {
    return response()->json($responseOrders, 400);
}
// Return Response
return response()->json($responseOrders, 200 );
    }

    /**
     * Retrieves a list of orders from the Shopify store.
     *
     * @param Request $request The incoming request instance.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the list of orders or an error message.
     */
    public function getOrders(Request $request)
    {
        // Check if the Shopify access token is present in the session
        $tokenResponse = $this->shopifyService->checkShopifyToken();
        $data = $tokenResponse->getData(true);

        if ($data['response'] === 'error') {
            return $tokenResponse; // Return the error response if the token is missing
        }

        $accessToken = $data['result']; // Extract the access token if successful

        // Validate request parameters
        $validationResponse = $this->shopifyService->validateRequestParameters($request, ['shop', 'code']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return error response if validation failed
        }
        $shop = $data['result']['shop'];

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $accessToken
        ])->get("https://{$shop}/admin/api/2023-10/orders.json");

        if ($response->failed()) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_fetch_orders_from_shopify'),
            ], 500);
        }

        return response()->json([
            'response' => 'success',
            'result' => $response->json(),
        ],200);
    }

    /**
     * Handle customer data request from Shopify.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function customerDataRequest(Request $request)
    {
       // Validate the Shopify request
        $validationResponse = $this->shopifyService->validateShopifyRequest($request);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed (potential hacker)
        }

        Log::info('Customer Data Request Received', $request->all());
        // Process the request as needed
        return response()->json(['message' => 'Customer data request received'], 200);
    }

    /**
     * Handle customer data erasure request from Shopify.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function customerDataErasure(Request $request)
    {
        // Validate the Shopify request
        $validationResponse = $this->shopifyService->validateShopifyRequest($request);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed (potential hacker)
        }

        Log::info('Customer Data Erasure:', $request->all());
        return response()->json(['message' => 'Customer data erasure request received'], 200);
    }

    /**
     * Handle shop data erasure request from Shopify.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function shopDataErasure(Request $request)
    {
       // Validate the Shopify request
       $validationResponse = $this->shopifyService->validateShopifyRequest($request);
       $data = $validationResponse->getData(true);

       if ($data['response'] === 'error') {
           return $validationResponse; // Return the error response if validation failed (potential hacker)
       }

        Log::info('Shop Data Erasure:', $request->all());
        return response()->json(['message' => 'Shop data erasure request received'], 200);
    }



}