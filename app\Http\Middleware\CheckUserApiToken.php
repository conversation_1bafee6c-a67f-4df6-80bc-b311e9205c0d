<?php

namespace App\Http\Middleware;

use App\Models\Sellers;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckUserApiToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $authGuard = auth()->guard('api');

        if (!$authGuard->check()) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. Please log in.',
                'status'   => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }

        $user = $authGuard->user();

        if (empty($user->id)) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. You are not a seller.',
                'status'   => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }

        // Retrieve the seller record
        $currentSeller = Sellers::where('user_id', $user->id)->firstOrFail();
        app()->instance('currentSeller', $currentSeller);

        return $next($request);
    }
}