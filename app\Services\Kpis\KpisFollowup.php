<?php
namespace App\Services\Kpis;

use App\Services\Orders\OrderStatusService;
use App\Services\Orders\FilterOrders;
use GlobalHelper;

class KpisFollowup {

    protected FilterOrders $filterOrders;
    protected OrderStatusService $OrderStatusService;
    
    public function __construct() {
        $this->filterOrders = new FilterOrders();
        $this->OrderStatusService = new OrderStatusService();
    }

    /**
     * Get Liste Statuts
     */
    public function FollowupKpis($data) {
        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);

        // Fetch the total leads
        $totalLeads = (float) $this->filterOrders->getOrders($data);

        // Initialize the result array
        $result = ['totalLeads' => $totalLeads,'rateTotal' => 100];

        // Calculate the results for each followup status
        $statusResults = $this->calculateStatusResults($data, $totalLeads);

        // Merge the results into the final output
        $result = array_merge($result, $statusResults);

        // Return the formatted final data containing all KPIs and their values
        return $result;
    }

    /**
     * Returns the base data used for filtering orders.
     */
    private function getBaseData($data) {
        return array_merge($data, [
            'followup' => 1,
            'count' => 1
        ]);
    }
    
    /**
     * Returns the list of followup statuses.
     */
    private function getFollowupStatuses() {
        $listeStatus = $this->OrderStatusService->getFollowupStatuses(['arKeys' => 1]);
        array_unshift($listeStatus, "new"); // Add "new" status at the beginning
        return $listeStatus;
    }

    /**
     * Calculates the results for each followup status.
     */
    private function calculateStatusResults($data, $totalLeads) {
        // Fetch the list of followup statuses
        $listeStatus = $this->getFollowupStatuses();

        $statusResults = [];

        // Iterate over the statuses and calculate the total and rate for each
        foreach ($listeStatus as $keyStatus) {
            $dataStatus = array_merge($data, ['listeFollowupStatus' => [$keyStatus]]);
            $followupResult = (float) $this->filterOrders->getOrders($dataStatus);
            
            // Clean and format the key
            $formattedKey = ucfirst(str_replace(["-", "_"], "", $keyStatus));

            // Assign values to the statusResults array
            $statusResults['total' . $formattedKey] = $followupResult;
            $statusResults['rate' . $formattedKey] = GlobalHelper::calculRate([
                'total' => $followupResult,
                'baseTotal' => $totalLeads
            ]);
        }

        // Calculate results for "others" status (statuses not included in the list)
        $dataOthers = array_merge($data, ['excludedFollowupStatus' => $listeStatus]);
        $followupOthers = (float) $this->filterOrders->getOrders($dataOthers);
        $statusResults['total_others'] = $followupOthers;
        $statusResults['rate_others'] = GlobalHelper::calculRate(['total' => $followupOthers, 'baseTotal' => $totalLeads]);

        return $statusResults;
    }
}
