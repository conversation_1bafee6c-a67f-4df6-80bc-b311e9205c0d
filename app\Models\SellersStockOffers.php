<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersStockOffers extends Model
{
    //
     protected $table = 'sellers_stock_offers';

	public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
	    'product_id',
	    'product_name',
	    'offer_name',
	    'total_free',
	    'total_paid',
	    'price_description',
	];

	/**
	 * Product
	 */
	public function product(){
        return $this->belongsTo(SellersStock::class, 'product_id', 'id');
    }

	/**
	 * Prices
	 */
	public function prices()
    {
        return $this->hasMany(SellersStockOffersPrices::class, 'offer_id', 'id');
    }

	protected $guarded = []; 
}
    