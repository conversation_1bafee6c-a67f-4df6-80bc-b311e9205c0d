<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class FacturesSellers extends Model
{
    //
     protected $table = 'factures_sellers';

	public $timestamps = true;

	protected $fillable = [
		'code',
		'facture_num',
		'statut',
		'statut_validate',
		'seller_id',
		'page_name',
		'page_id',
		'seller_name',
		'seller_bank',
		'seller_rib',
		'agence_id',
		'agence_name',
		'total_facture',
		'total_comission',
		'total_credit',
		'motif_total_credit',
		'total_frais_versement',
		'total_reste',
		'total_livraison',
		'total_retours',
		'total_fulfillment',
		'total_firstmile',
		'total_custom_surcharge',
		'total_confirmed_delivered',
		'total_outbound_callcenter',
		'total_outbound_followup',

		'vat_pourc',
		'vat_total_price',
		'cod_fees_declared',
		'cod_fees_deducted',
		'cod_fees_total_price',
		'cod_clearance_declared',
		'cod_clearance_deducted',
		'cod_clearance_total_price',

		'confirmed_at',
		'confirmed_by',
		'responsable_paiement',
		'responsable_id',
		'responsable_type',
		'statut_paiement',
		'method_paiement',
		'price_method_paiement',
		'barecode_colis',
		'colis_agence_name',
		'city_name',
		'start_date',
		'end_date',
		'seller_company',
		'facture_type',
		'total_upsells',
		'total_confirmed',
		'is_verified',
		'created_by',
		'updated_by',
		'deleted_by',
	];

	/**
	 * details Fees
	 */
	public function detailsFees(): HasMany{
		return $this->hasMany(FacturesSellersDetails::class, 'facture_id', 'id')->with(['order']);
	}
    /**
     * Get all fees for delivered parcels.
     */
    public function deliveredFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'delivered_undelivered')->where('colis_statut', 'delivered');
    }

    /**
     * Get all fees for returned (undelivered) parcels.
     */
    public function returnFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'delivered_undelivered')->where('colis_statut', 'undelivered');
    }

    /**
     * Get all fees related to shipped parcels.
     */
    public function shippedFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'shipping');
    }

    /**
     * Get all fees related to confirmation deliveries by call center.
     */
    public function confirmationFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'callcenter');
    }

    /**
     * Get all fees related to upsell orders.
     */
    public function upsellFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'upsell');
    }

    /**
     * Get all fees related to outbound confirmation calls.
     */
    public function confirmationCallsFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'outbound_callcenter');
    }

    /**
     * Get all fees related to outbound follow-up calls.
     */
    public function followupCallsFees(): HasMany {
        return $this->detailsFees()->where('facture_type', 'outbound_followup');
    }

	
	protected $guarded = []; 
}
    