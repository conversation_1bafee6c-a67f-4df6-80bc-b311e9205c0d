<?php

namespace App\Traits;

use GlobalHelper;

trait FilterQuery{

      /**
     * Apply custom column selection if provided, otherwise use default columns.
     */
    private function applyCustomColumns(): void {
        $customColumns = $this->data['custom_columns'] ?? [];

        if (!empty($customColumns)) {
            $this->query->select($customColumns);
        } else {
            $this->data = $this->FieldsService->DefaultColumns($this->columnsType, $this->data);
            if (!empty($this->data['columns'])) {
                $this->query->select($this->data['columns']);
            }
        }
    }


    /**
     * Apply ordering to the query based on provided parameters.
     */
    private function applyOrdering(): void {
        $orderBy = $this->data['orderby'] ?? "{$this->baseTable}.id";
        $orderDirection = $this->data['ordering'] ?? 'DESC';

        $this->query->orderBy($orderBy, $orderDirection);
    }

    /**
     * Fetch results based on pagination and other parameters.
     */
    private function fetchResults($formatterService = null) {
        $id = $this->data['id'] ?? null;
        $count = $this->data['count'] ?? false;
        $perPage = $this->data['perPage'] ?? env('PER_PAGE', 15);
        $noPaginate = $this->data['noPaginate'] ?? false;
        $toArray = $this->data['toArray'] ?? false;
        $first = $this->data['first'] ?? false;
        $noFormatter = $this->data['noFormatter'] ?? false;
        $totalPrices = $this->data['totalPrices'] ?? false;
        $execQuery = $this->data['execQuery'] ?? false;

        if($execQuery == 1){
            //die(GlobalHelper::execQuery($this->query));
        }
        
        // If count is requested, return the total count of matching records
        if ($count) {
            return $this->query->count();

        } elseif ($totalPrices) {
            return $this->query->sum('price');
        
        } elseif ($toArray) {
            $results = $this->query->get()->toArray();

        } elseif ($id || $first) {
            $results = $this->query->first();
            $first = true;
            $noPaginate = true;

        } else {
            $results = $noPaginate ? $this->query->get() : $this->query->paginate($perPage);
        }

        // Return Result
        return $this->formatResponse($results, $noFormatter, $formatterService , $first, $noPaginate);
    }

    /**
     * format Response
     */
    function formatResponse($results, $noFormatter = false, $formatterService = null, $first = null, $noPaginate = false) {
        // Return Result without formatter
        if ($noFormatter) return $results;

        // Prepare the payload array for the response
        $payload = [
            'results' => $formatterService
                ? $formatterService->formatterResult([
                    "items" => $first == 1 ? [$results] : ($noPaginate ? $results : $results->items()),
                    'first' => $first,
                ])
                : $results,
            'first' => $first,
            'noPaginate' => $noPaginate,
            'layout' => $this->data['layout'] ?? null,
            'type' => $this->columnsType,
        ];

        //  Add additional data to the payload
        if ($formatterService) {
            $payload['initialResults'] = $results;
            $payload['resultFormatted'] = 1;
        }

        // Return the formatted response
        return $this->FieldsService->ResponseResults($payload);
    }
}
