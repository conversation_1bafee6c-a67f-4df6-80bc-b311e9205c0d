<?php

namespace App\Services\Sourcing;

use App\Models\SourcingJournal;
use App\Models\SourcingRequest;

class SourcingJournalService
{
    /**
     * Create a new journal entry
     *
     * @param array $data
     * @return SourcingJournal
     */
    public function createEntry(array $data): SourcingJournal
    {
        $user = auth('api')->user();

        if (!$user) {
            throw new \Exception('User not authenticated');
        }


        return SourcingJournal::create([
            'user_id' => $user->id,
            'user_name' => $user->name,
            'sourcing_id' => $data['sourcing_id'],
            'sourcing_code' => $data['sourcing_code'],
            'sourcing_seller' => $data['sourcing_seller'],
            'sourcing_status' => $data['sourcing_status'],
            'action' => $data['action'],
        ]);
    }

    /**
     * Log a sourcing request creation
     *
     * @param SourcingRequest $sourcingRequest
     * @return SourcingJournal
     */
    public function logCreation(SourcingRequest $sourcingRequest): SourcingJournal
    {
        return $this->createEntry([
            'sourcing_id' => $sourcingRequest->id,
            'sourcing_code' => $sourcingRequest->request_code,
            'sourcing_seller' => $sourcingRequest->seller_name,
            'sourcing_status' => $sourcingRequest->status,
            'action' => 'created',
        ]);
    }

    /**
     * Log a sourcing request update
     *
     * @param SourcingRequest $sourcingRequest
     * @param string $action
     * @return SourcingJournal
     */
    public function logUpdate(SourcingRequest $sourcingRequest, string $action): SourcingJournal
    {
        return $this->createEntry([
            'sourcing_id' => $sourcingRequest->id,
            'sourcing_code' => $sourcingRequest->request_code,
            'sourcing_seller' => $sourcingRequest->seller_name,
            'sourcing_status' => $sourcingRequest->status,
            'action' => $action,
        ]);
    }

    /**
     * Log a sourcing request status change
     *
     * @param SourcingRequest $sourcingRequest
     * @param string $oldStatus
     * @return SourcingJournal
     */
    public function logStatusChange(SourcingRequest $sourcingRequest, string $oldStatus): SourcingJournal
    {
        return $this->createEntry([
            'sourcing_id' => $sourcingRequest->id,
            'sourcing_code' => $sourcingRequest->request_code,
            'sourcing_seller' => $sourcingRequest->seller_name,
            'sourcing_status' => $sourcingRequest->status,
            'action' => 'status_changed',
        ]);
    }

    /**
     * Get journal entries for a sourcing request
     *
     * @param int $sourcingId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getEntriesForRequest(int $sourcingId)
    {
        return SourcingJournal::where('sourcing_id', $sourcingId)
            ->orderBy('created_at', 'desc')
            ->get();
    }
}