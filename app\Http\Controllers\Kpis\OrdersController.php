<?php

namespace App\Http\Controllers\Kpis;

use App\Http\Controllers\Controller;
use App\Services\Kpis\KpisOrders;
use App\Services\Orders\OrderStatusService;
use App\Services\Validator\ProductsValidator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class OrdersController extends Controller{
    protected KpisOrders $KpisOrders;
    protected ProductsValidator $ProductsValidator;
    protected OrderStatusService $OrderStatusService;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->KpisOrders = new KpisOrders();
        $this->ProductsValidator = new ProductsValidator();
        $this->OrderStatusService = new OrderStatusService();
    }

    /**
     * Funds Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function orders(Request $request): JsonResponse{
        // Validate the request
        $validateData = $this->validateData($request);
        if ($validateData['response'] === 'error') { return response()->json($validateData, 400); }

        // Call Method
        $method = 'kpis' . ucfirst($request->type);
        if (method_exists($this->KpisOrders, $method)) {
            $fundsKpis = $this->KpisOrders->$method($request->all());
        } else {
            $fundsKpis = $this->KpisOrders->kpisStatus($request->all(),$request->type);
        }
        
        
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $fundsKpis,
        ]);
    }

     /**
     * Validate Data
     */
    private function validateData(Request $request): ?array{
        $listeStatus = array_keys($this->OrderStatusService->mapStandardStatuses(["getList" => 1]));

        // Validate the uploaded file using Laravel's Validator
        $validator = Validator::make($request->all(), [
            'type' => ['required', Rule::in(array_merge($listeStatus,['all','reals','shipped','upselling','others']))]
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message'  => implode(", ", $validator->messages()->all()),
            ];
        }
        // Return success response if validation passes
        return [
            'response' => 'success'
        ];
    }
}