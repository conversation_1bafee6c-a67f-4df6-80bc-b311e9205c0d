<?php

namespace App\Http\Controllers\Invoices;

use App\Http\Controllers\Controller;
use App\Services\Invoices\FilterInvoices;
use App\Services\Invoices\InvoicesService;
use App\Services\Orders\OrderStatusService;
use App\Services\Validator\InvoicesValidator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class InvoicesFeesController extends Controller{
    protected InvoicesValidator $InvoicesValidator;
    protected FilterInvoices $FilterInvoices;
    protected InvoicesService $InvoicesService;
    protected OrderStatusService $OrderStatusService;

    /**
     * Construct
     */
    public function __construct(){
        $this->InvoicesValidator = new InvoicesValidator();
        $this->FilterInvoices = new FilterInvoices();
        $this->InvoicesService = new InvoicesService();
        $this->OrderStatusService = new OrderStatusService();
    }

    /**
     * Retrieve and return a list of filtered invoices.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function fees(Request $request,$id): JsonResponse{
        // Validate the request
        $validateFees = $this->validateData($request,$id);
        if ($validateFees['response'] === 'error') { return response()->json($validateFees, 400); }

        // Get Invoice
        $invoice = $validateFees["rowInvoice"] ?? [];

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->formatterInvoice($invoice,$request),
        ]);
    }

    /**
     * load Fees Shipped
     */
    private function formatterInvoice($invoice,$request){

        if($request->count)
        {
            return count($invoice->{$this->camelToSnake($request->type.'Fees')});
        }
        // Fees Details
        $feesDetails = $this->formatFeesDetails($invoice,$request->type);

        // Formatter Invoice
        unset($invoice->{$this->camelToSnake($request->type.'Fees')});
        $invoice->ordersCount = count($feesDetails);
        $invoice->feesDetails = $feesDetails;

       // Return new invoice
       return $invoice;
    }

    /**
     * format Fees Details
     */
    private function formatFeesDetails($invoice,$type){
        $results = [];
        $feesDetails = $invoice->{$this->camelToSnake($type.'Fees')};
        // Loop Fees
        if($feesDetails){foreach($feesDetails as $fees){

            $rowFees = [
                "orderId" => $fees->colis_id,
                "orderNum" => $fees->order->order_num,
                "orderCode" => $fees->order->barecode,
                "originCountry" => $fees->from_name,
                "destinationCountry" => $fees->to_name,
                "usdPrice" => $fees->colis_price,
                "orderPrice" => $fees->price_origine,
                "currency" => $fees->currency,
                "shippedAt" => $fees->order->shipped_at,
            ];

            // order Status
            if($type == "followupCalls"){
                $rowFees['followpStatus'] = $fees->colis_statut;
                $rowFees['callDate'] = $fees->call_date;
            }else{
                $rowFees['callDate'] = $fees->call_date;
                $rowFees['orderStatus'] = $this->OrderStatusService->mapStandardStatuses(['originStatus' => $fees->colis_statut]);
            }

            // Add Shipping Fees
            if($type == "shipped"){
                $rowFees["shippingFees"] = $fees->shipping_price;
                $rowFees["firstMileFees"] = $fees->firstmile_price;
                $rowFees["customSurcharge"] = $fees->custom_surcharge;
                $rowFees["fulfillment"] = $fees->price_fulfillment;
            }

            // Add Delivered Fees
            if($type == "delivered"){
                $rowFees["deliveredFees"] = $fees->price_confirmeddelivered;
                $rowFees["codFees"] = $fees->cod_fees;
                $rowFees["vat"] = $fees->clearance_fees;

            }else{
                $rowFees["fees"] = $fees->final_comission;
            }

             $results[] = $rowFees;
        }}

        // Return Result
        return $results;
    }

    /**
     * Validate
     */
    private function validateData(Request $request,$id): ?array{
        // Validate the uploaded file using Laravel's Validator
        $validator = Validator::make($request->all(), [
            'type' => ['required', Rule::in(['shipped','delivered','return','confirmation','upsell','confirmationCalls','followupCalls'])]
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message'  => implode(", ", $validator->messages()->all()),
            ];
        }

        // Validate Product
        $validateInvoice = $this->InvoicesValidator->validateInvoice([
            'id' => $id,
            'layout' => 'general,details',
            'with' => [ $request->type.'Fees',]
        ]);
        if($validateInvoice['response'] == 'error'){ return $validateInvoice; }

        // Return success response if validation passes
        return [
            'response' => 'success',
            'rowInvoice' => $validateInvoice['rowInvoice'],
        ];
    }

    /**
     * camel To Snake
     */
    function camelToSnake($input) {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
    }
}