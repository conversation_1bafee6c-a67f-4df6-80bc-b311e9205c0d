<?php
namespace App\Services\Export;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx as ReaderXlsx;

class ExcelExportService
{
    /**
     * @var Spreadsheet
     */
    protected $spreadsheet;

    /**
     * ExcelExportService constructor.
     * Initializes the PhpSpreadsheet library and creates a new spreadsheet instance.
     */
    public function __construct(){
        // Initialize a new spreadsheet object.
        $this->spreadsheet = new Spreadsheet();
    }

    /**
     * Generates an Excel file with customizable sheets and data.
     *
     * @param array $options Options for generating the Excel file.
     * 
     * @return void
     */
    public function generateExcel($options = [])
    {
        $listeSheets = $options['listeSheets'] ?? null;
        $excelName = $options['title'] ?? 'Export'; // Default name for the file if no title is provided.

        // If no custom sheets are provided, return immediately.
        if (!$listeSheets) {
            return;
        }

        // Remove default sheet that comes with a new spreadsheet.
        $this->spreadsheet->removeSheetByIndex(0);

        // Loop through each sheet in the 'listeSheets' array.
        foreach ($listeSheets as $rowSheet) {
            $this->addSheetToSpreadsheet($rowSheet);
        }

        // Save to file and initiate download.
        return $this->downloadExcel($excelName);
    }

     /**
     * Converts a column index (integer) to an Excel column letter.
     *
     * @param int $index The column index (1-based).
     * 
     * @return string The corresponding column letter.
     */
    public static function stringFromColumnIndex($index)
    {
        return Coordinate::stringFromColumnIndex($index);
    }

    /**
     * Adds a sheet to the spreadsheet.
     *
     * @param array $rowSheet Sheet data and customization options.
     */
    protected function addSheetToSpreadsheet($rowSheet)
    {
        // Create a new worksheet.
        $worksheet = new Worksheet($this->spreadsheet, $rowSheet['name']);
        $this->spreadsheet->addSheet($worksheet, 0);
        $worksheet->fromArray($rowSheet['data']);

        // Apply custom styles if provided.
        $this->applyCustomStyles($worksheet, $rowSheet['customParams'] ?? []);
    }

    /**
     * Applies custom styles to a worksheet.
     *
     * @param Worksheet $worksheet The worksheet to modify.
     * @param array $customParams Styling options like width, color, and merging.
     */
    protected function applyCustomStyles(Worksheet $worksheet, $customParams)
    {
        // Apply Wrap Text
        if (isset($customParams['columnsWrap'])) {
            $this->applyWrapText($worksheet, $customParams['columnsWrap']);
        }

        // Apply Column Width
        if (isset($customParams['columnsWidth'])) {
            $this->applyColumnWidth($worksheet, $customParams['columnsWidth']);
        }

        // Apply Columns Style
        if (isset($customParams['columnsStyle'])) {
            $this->applyColumnsStyle($worksheet, $customParams['columnsStyle']);
        }

        // Apply Merge Cells
        if (isset($customParams['mergeCells'])) {
            $this->applyMergeCells($worksheet, $customParams['mergeCells']);
        }

        // Apply Header Cells
        if (isset($customParams['headerCells'])) {
            $this->applyHeaderCells($worksheet, $customParams['headerCells']);
        }
    }

    /**
     * Apply Wrap Text to specific columns.
     *
     * @param Worksheet $worksheet The worksheet to modify.
     * @param array $columnsWrap List of columns to apply wrap text.
     */
    protected function applyWrapText(Worksheet $worksheet, $columnsWrap)
    {
        foreach ($columnsWrap as $column_name) {
            // Loop through each row in the column and apply wrap text
            for ($i = 1; $i <= count($worksheet->toArray()) + 1; $i++) {
                $worksheet->getStyle($column_name . $i)->getAlignment()->setWrapText(true);
            }
        }
    }

    /**
     * Apply Column Width to columns.
     *
     * @param Worksheet $worksheet The worksheet to modify.
     * @param mixed $columnsWidth Either an array of column widths or a single width for all columns.
     */
    protected function applyColumnWidth(Worksheet $worksheet, $columnsWidth)
    {
        if (is_array($columnsWidth)) {
            // If columnsWidth is an array, set individual column widths
            foreach ($columnsWidth as $column_name => $width) {
                $worksheet->getColumnDimension($column_name)->setWidth($width, 'pt');
            }
        } else {
            // If columnsWidth is a single value, set all columns to that width
            foreach ($worksheet->getColumnIterator() as $column) {
                $worksheet->getColumnDimension($column->getColumnIndex())->setWidth($columnsWidth);
            }
        }
    }

    /**
     * Apply custom styles (background color and text color) to columns.
     *
     * @param Worksheet $worksheet The worksheet to modify.
     * @param array $columnsStyle List of columns with styling information.
     */
    protected function applyColumnsStyle(Worksheet $worksheet, $columnsStyle)
    {
        foreach ($columnsStyle as $colstyle) {
            // Apply background color
            $worksheet->getStyle($colstyle['column'])
                ->getFill()
                ->setFillType(Fill::FILL_SOLID)
                ->getStartColor()
                ->setARGB($colstyle['bgColor']);

            // Apply text color
            $worksheet->getStyle($colstyle['column'])
                ->getFont()
                ->getColor()
                ->setARGB($colstyle['textColor']);
        }
    }

    /**
     * Merge specific cells and apply formatting (center alignment, bold, etc.).
     *
     * @param Worksheet $worksheet The worksheet to modify.
     * @param array $mergeCells List of merge cell ranges.
     */
    protected function applyMergeCells(Worksheet $worksheet, $mergeCells)
    {
        foreach ($mergeCells as $mergeColumns) {
            // Merge the cells
            $worksheet->mergeCells($mergeColumns);

            // Center the text in the merged cell
            $worksheet->getStyle($mergeColumns)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $worksheet->getStyle($mergeColumns)->getAlignment()->setVertical(Alignment::VERTICAL_CENTER);

            // Make the text bold
            $worksheet->getStyle($mergeColumns)->getFont()->setBold(true);

            // Add padding (internal space within cells)
            $worksheet->getStyle($mergeColumns)->getAlignment()->setIndent(1); // Adds space on the left

            // Set Background Color (light blue in this case)
            $worksheet->getStyle($mergeColumns)->getFill()->setFillType(Fill::FILL_SOLID);
            $worksheet->getStyle($mergeColumns)->getFill()->getStartColor()->setRGB('ADD8E6'); // Light blue

            // Set Text Color (black)
            $worksheet->getStyle($mergeColumns)->getFont()->getColor()->setRGB('000000'); // Black

            // Optional: Add borders around the merged cells
            $worksheet->getStyle($mergeColumns)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        }
    }

    /**
     * Apply styles to header cells (bold text, background color, etc.).
     *
     * @param Worksheet $worksheet The worksheet to modify.
     * @param array $headerCells List of header cells to style.
     */
    protected function applyHeaderCells(Worksheet $worksheet, $headerCells)
    {
        foreach ($headerCells as $headerColumn) {
            // Make the text bold
            $worksheet->getStyle($headerColumn)->getFont()->setBold(true);

            // Set Background Color (light gray in this case)
            $worksheet->getStyle($headerColumn)->getFill()->setFillType(Fill::FILL_SOLID);
            $worksheet->getStyle($headerColumn)->getFill()->getStartColor()->setRGB('CCCCCC'); // Light gray

            // Set Text Color (black)
            $worksheet->getStyle($headerColumn)->getFont()->getColor()->setRGB('000000'); // Black

            // Optional: Add borders around the header cells
            $worksheet->getStyle($headerColumn)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
        }
    }
    

    /**
     * Downloads the Excel file.
     *
     * @param string $fileName The name of the exported file.
     * 
     * @return void
     */
    protected function downloadExcel($fileName){
        ob_end_clean();
        $writer = new Xlsx($this->spreadsheet);
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . urlencode(Str::slug($fileName) . ".xlsx") . '"');
        $writer->save('php://output');
        exit();
    }

    /*
        Get Data From Excel File
    */
    public function getDataFromExcel($options = []) {
        // Extract options with default values if not set
        $fileName = $options['fileName'] ?? null;
        $withFilePath = $options['withFilePath'] ?? null;
        $sheetIndex = $options['sheetIndex'] ?? null;
    
        // Check if file is uploaded and the fileName is provided
        if ($fileName && isset($_FILES[$fileName])) {
            // Get file extension
            $info = pathinfo($_FILES[$fileName]['name']);
            $ext = strtolower($info['extension']);  // Ensure extension is lowercase
    
            // Generate a new name for the file if not provided
            $newName = ($options['newName'] ?? "log-" . time()) . "." . $ext;
    
            // Define the folder path to store the file
            $pathFile = public_path() . DIRECTORY_SEPARATOR . 'logs' . DIRECTORY_SEPARATOR . 'excels';
    
            // Check if the file extension is 'xlsx'
            if ($ext === "xlsx") {
                // Create the directory if it doesn't exist
                if (!file_exists($pathFile)) {
                    mkdir($pathFile, 0777, true); // Create the folder with full permissions
                }
    
                // Set the full path for the uploaded file
                $inputFileName = $pathFile . DIRECTORY_SEPARATOR . $newName;
                $target = $inputFileName;
    
                // Move the uploaded file to the target location
                if (move_uploaded_file($_FILES[$fileName]['tmp_name'], $target)) {
                    // Parse the Excel data from the uploaded file
                    $sheetData = $this->parseExcelDataFromFile([
                        'filePath' => $inputFileName,
                        'sheetIndex' => $sheetIndex,
                    ]);
    
                    // Prepare the result based on the "withFilePath" option
                    $results = $withFilePath === 1
                        ? ['sheetData' => $sheetData, 'filePath' => $inputFileName] // Include file path if specified
                        : $sheetData; // Only return sheet data if not
    
                    return $results; // Return the results
                }
            }
        }
    
        return [];
    }
    
    /*
        Parse Data From Excel File
    */
    private function parseExcelDataFromFile($options = []) {
        // Extract the file path and sheet index from options, with defaults if not provided
        $filePath = $options['filePath'] ?? null;
        $sheetIndex = $options['sheetIndex'] ?? 0;
    
        // Initialize the Excel reader for xlsx files
        $reader = new ReaderXlsx();
        $reader->setReadDataOnly(true); // Set to read only the data, ignoring formatting
    
        // Load the spreadsheet from the provided file path
        $spreadsheet = $reader->load($filePath);
    
        // Get the total number of sheets in the spreadsheet
        $totalSheets = $spreadsheet->getSheetCount();
    
        // Validate the sheet index. If it's out of range, return an empty array
        if ($sheetIndex >= $totalSheets) {
            return []; // Return empty array if the sheet index is invalid
        }
    
        // Get the sheet data for the selected sheet index
        $sheet = $spreadsheet->getSheet($sheetIndex);
    
        // Convert the sheet data to an array
        $data = $sheet->toArray();
    
        // Format the data to map column letters (A, B, C, ...) to values
        $formattedData = [];
        $i = 0;
        foreach ($data as $kIndex => $subData) {
            if($i > 0){
                $newSubData = [];
                $index = "A"; // Start with column 'A'
                
                // Iterate through each row and assign the values to column letters
                foreach ($subData as $value) {
                    $newSubData[$index] = $value;
                    $index++; // Move to the next column
                }
        
                // Store the formatted data for each row
                $formattedData[$kIndex] = $newSubData;
            }
            $i++;
        }
    
        // Return the formatted data
        return $formattedData;
    }    
}
