<?php
namespace App\Services\Warehousing;

use App\Services\Shipping\FilterWarehouses;

class ProductsService{
    protected FilterWarehouses $FilterWarehouses;
    protected FilterProducts $FilterProducts;

    /**
     * Inject the FilterProducts service into the controller
     */
    public function __construct(){
        $this->FilterWarehouses = new FilterWarehouses();
        $this->FilterProducts = new FilterProducts();
    }
    
    /**
     * Product Types
    */
    public function getProductTypes(){
        return [
            'simple' => __('products.Simple product'),
            'variable' => __('products.Variable product'),
        ];
    }

    /**
     * Product Categories
    */
    public function getProductCategories(){
        return [
            'normal' => __('products.Normal Product'),
            'sensitive' => __('products.Sensitive Product (Cosmetics/Liquid etc)'),
            'battery' => __('products.Product with Battery'),
            'powerbank' => __('products.Pure Battery/Power Bank'),
        ];
    }

    /*
		Combanition arrays
    */
	public static function Combinations($arrays, $i = 0){
		if (!isset($arrays[$i])) {
			return array();
		}

		if ($i == count($arrays) - 1) {
			return $arrays[$i];
		}

		// get combinations from subsequent arrays
		$tmp = Self::Combinations($arrays, $i + 1);

		$result = array();

		// concat each array from tmp with each element from $arrays[$i]
		foreach ($arrays[$i] as $v) {
			foreach ($tmp as $t) {
				$result[] = is_array($t) ?
					array_merge(array($v), $t) :
					array($v, $t);
			}
		}

		return $result;
	}
}