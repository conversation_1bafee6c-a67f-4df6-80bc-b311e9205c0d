<?php

namespace App\Http\Controllers\Orders;

use App\Http\Controllers\Controller;
use App\Services\Orders\OrderStatusService;
use App\Services\Orders\OrderFlowService;
use App\Services\Orders\OrderService;
use App\Services\Validator\OrdersValidator;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

class OrderFlowController extends Controller{
    protected OrderFlowService $OrderFlowService;
    protected OrdersValidator $OrdersValidator;
    protected OrderStatusService $OrderStatusService;
    protected OrderService $OrderService;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->OrderFlowService = new OrderFlowService();
        $this->OrdersValidator = new OrdersValidator();
        $this->OrderStatusService = new OrderStatusService();
        $this->OrderService = new OrderService();
    }

    /**
     * Order Customer
     */
    public function customer($id){
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['id' => $id, 'layout' => 'general,details']);
        if ($validateOrder['response'] === 'error') { return response()->json($validateOrder, 404); }

        // Extract Infos
        $rowOrder = $validateOrder['rowOrder'];
        $consignee = $rowOrder->consignee;
        $addressDetails = $this->OrderService->extractAddressDetails($rowOrder);
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => [
                'contact'        => $consignee->contact,
                'mobileNumber'   => $consignee->mobileNumber,
                'whatsappNumber' => $consignee->whatsappNumber,
                'phoneNumber'    => $consignee->phoneNumber,
                'language'       => $consignee->language,
                'country'        => $consignee->country,
                'city'           => $addressDetails['city'],
                'area'           => $addressDetails['area'],
                'street'         => $addressDetails['street'],
                'houseNumber'    => $addressDetails['houseNumber'],
                'nearestPlace'   => $addressDetails['nearestPlace'],
                'address'        => $consignee->address,
                'gps' => $consignee->latitude && $consignee->longitude ? $consignee->latitude . ',' . $consignee->longitude : null,
            ]
        ]);
    }

    /**
     * Order Products
     */
    public function products(int $id){
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['id' => $id, 'layout' => 'general,details']);
        if ($validateOrder['response'] === 'error') { return response()->json($validateOrder, 404); }

        // Extract Order Info
        $rowOrder = $validateOrder['rowOrder'];
        $products = $this->OrderFlowService->getProducts(['orderId' => $rowOrder->id]);

        $totalProducts = $this->OrderFlowService->getProducts(['orderId' => $rowOrder->id, 'sumQty' => 1]);

        // Return JSON Response
        return response()->json([
            'response' => 'success',
            'count' => $totalProducts,
            'result' => $this->formatProducts($products),
        ]);
    }

    /**
     * Order calls
     */
    public function calls($id): JsonResponse{
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['id' => $id, 'layout' => 'general,details']);
        if ($validateOrder['response'] === 'error') { return response()->json($validateOrder, 404);}

        // Extract Infos
        $rowOrder = $validateOrder['rowOrder'];

        // Format Calls
        $listeCalls = (object) $this->OrderFlowService->getOrderCalls(['orderId' => $rowOrder->id]);
        $formatterCalls = $listeCalls->map(function($call) {
            return $this->formatCall($call);
        })->toArray();

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'count' => count($formatterCalls),
            'result' => $formatterCalls,
        ]);
    }

     /**
     * Order Confirmation
     */
    public function confirmation($id): JsonResponse{
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['id' => $id, 'layout' => 'general,details']);
        if ($validateOrder['response'] === 'error') { return response()->json($validateOrder, 404);}

        // Extract Infos
        $rowOrder = $validateOrder['rowOrder'];

        // Format confirmation
        $listeConfirmations = (object) $this->OrderFlowService->getOrderConfirmation(['orderId' => $rowOrder->id,'type' => 'confirmation']);
        $formatterConfirmations = $listeConfirmations->map(function($rowAction) {
            return $this->formatConfirmation($rowAction);
        })->toArray();

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'count' => count($formatterConfirmations),
            'result' => $formatterConfirmations,
        ]);
    }

    /**
     * Order Shipping
     */
    public function shipping($id): JsonResponse{
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['id' => $id, 'layout' => 'general,details']);
        if ($validateOrder['response'] === 'error') { return response()->json($validateOrder, 404);}

        // Extract Infos
        $rowOrder = $validateOrder['rowOrder'];

        // Format Calls
        $listeShipping = (object) $this->OrderFlowService->getOrderConfirmation(['orderId' => $rowOrder->id,'type' => 'shipping']);
        $formatterShipping = $listeShipping->map(function($rowAction) {
            return $this->formatConfirmation($rowAction);
        })->toArray();

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'count' => count($formatterShipping),
            'result' => $formatterShipping,
        ]);
    }

    /**
     * Order Followup
     */
    public function followup($id): JsonResponse{
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['id' => $id, 'layout' => 'general,details']);
        if ($validateOrder['response'] === 'error') { return response()->json($validateOrder, 404);}

        // Extract Infos
        $rowOrder = $validateOrder['rowOrder'];

        // Format Calls
        $listeFollowup = (object) $this->OrderFlowService->getOrderFollowup(['orderId' => $rowOrder->id]);
        $formatterFollowup = $listeFollowup->map(function($call) {
            return $this->formatFollowup($call);
        })->toArray();

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'count' => count($formatterFollowup),
            'result' => $formatterFollowup,
        ]);
    }

    /**
     * Format product data into a structured array.
     */
    private function formatProducts(Collection $products): array{

        return $products->map(fn($product) => [
            'name' => $product->variante_name ?: $product->main_product_name,
            'sku' => $product->variante_sku ?: $product->main_product_sku,
            'quantity' => $product->quantity,
            'price' => $product->price ? $product->price .' '.$product->colis_curr : null,
            'size' => $product->size,
            'color' => $product->color,
        ])->toArray();
    }

    /**
     * format Call
     */
    private function formatCall($call){
        switch ($call->reponse) {
            case 'shipped_reported':
            case 'reported':
                if ($call->time_reported && $call->time_reported !== "0000-00-00 00:00:00") {
                    $answer = "The customer wishes this order at " . date('d/m/Y H:i:s', strtotime($call->time_reported));
                } else {
                    $answer = "Schedule to " . date('d/m/Y H:i', strtotime($call->date_reported));
                }
                break;
            case 'delivered':
                $answer = "Delivered";
                break;
            default:
                $answer = $this->OrderStatusService->mapDynamicStatuses([
                    'originStatus' => $call->reponse,
                ]);
                break;
        }

        // Generate comment
        $comment = null;
        if (!is_null($call->slug_reponse) && !is_null($call->comment)) {
            $comment = $call->slug_reponse . ' - ' . $call->comment;
        }else{
            $comment = $call->slug_reponse ? :  $call->comment;
        }

        // Format and return the result
        return [
            'date' => $call->datecall,
            'answer' => $answer,
            'comment' => $comment,
        ];
    }

     /**
     * format followup
     */
    private function formatFollowup($call){
        // Format and return the result
        $status = $this->OrderStatusService->followupStatusList([
            'originStatus' => $call->feedbacktype,
        ]);

        // Check Schedule Date
        if($call->scheduledate && $call->scheduledate != "0000-00-00 00:00:00"){
            $status = $status." - " . date('d/m/Y H:i', strtotime($call->scheduledate));
        }

        // Return Format Followup
        return [
            'date' => $call->action_date,
            'feedback' => $call->feedbacktype,
            'status' => $status,
            'comment' => $call->rejectreason? :$call->comment,
        ];
    }

    /**
     * Formats the confirmation status of an order action.
     */
    private function formatConfirmation($rowAction){
        // Retrieve the new status and slug status from the action
        $newStatut = $rowAction->new_statut;
        $sluganStatut = $rowAction->slugan_statut;

        // Get the list of status keys mapped dynamically
        $listeStatusKeys = $this->OrderStatusService->mapDynamicStatuses(['arKeys' => 1]);

        // If the slug status is set, use it as the label
        if ($sluganStatut) {
            $labelStatut = $sluganStatut;
        } elseif (in_array($newStatut, $listeStatusKeys ?? [], true)) {
            $labelStatut = $this->OrderStatusService->mapDynamicStatuses(['originStatus' => $newStatut]);

            if ($newStatut === "reported" && !empty($rowAction->date_reported) && $rowAction->date_reported !== "0000-00-00") {
                $labelStatut = "Schedule to " . date('d/m/Y H:i', strtotime($rowAction->date_reported));
            }
        } else {
            // If the slug status is set, use it as the label
            $statusMapping = [
                "create-colis" => "Order created",
                "edit-colis" => "Order modified",
                "expired" => "Order expired",
            ];

            $labelStatut = $statusMapping[$newStatut] ?? "";
        }

        // Return the formatted status data if a valid label exists
        return $labelStatut ? [
            'date' => $rowAction->created_at->format('Y-m-d H:i:s'),
            'status' => $labelStatut,
        ] : null;
    }

}
