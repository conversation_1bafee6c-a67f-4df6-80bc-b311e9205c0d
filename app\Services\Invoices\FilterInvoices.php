<?php
namespace App\Services\Invoices;

use App\Models\FacturesSellers;
use App\Services\Fields\FieldsService;
use App\Services\Fields\FormatterInvoices;
use App\Traits\FilterQuery;
use SellersHelper;

class FilterInvoices {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $FormatterInvoices;
    protected $currentSeller;
    protected $columnsType;
    use FilterQuery;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->FormatterInvoices = new FormatterInvoices();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();

        // Table name
        $this->baseTable = 'factures_sellers';
        $this->columnsType = 'invoices';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getInvoices(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // General Conditions
        $this->applyGeneralFilters();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();
        
        // Fetch and return the final filtered results
        return $this->fetchResults($this->FormatterInvoices);
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = FacturesSellers::where("{$this->baseTable}.statut_validate", 'open')
                                        ->where("{$this->baseTable}.seller_id", $this->currentSeller->id)
                                        ->where('is_verified',1)
                                        ->where('is_published',1);

        // Check Detail Layout
        if(($this->data['with'] ?? null)){
            $this->query->with($this->data['with']);
        }
    }

    /**
     * Apply general filters to the query.
     */
    private function applyGeneralFilters(){
        // Get the parent filter value from data, default to null if not set
        $id = $this->data["id"] ?? null;
        $invoiceNum = $this->data["invoiceNum"] ?? null;
        $startDate = $this->data['startDate'] ?? null;
        $endDate = $this->data['endDate'] ?? null;
        $status = $this->data['status'] ?? null;

        // Filter By Num
        if($id){ $this->query->where("{$this->baseTable}.id", $id); }
        
        // Filter By ID
        if($invoiceNum){ $this->query->where("{$this->baseTable}.facture_num", "like","%".$invoiceNum."%"); }
        
        // Filter by status
        if($status){ $this->query->where("{$this->baseTable}.statut", $status); }

        // Apply date filters
        if ($startDate) {
            $this->query->whereDate("{$this->baseTable}.created_at", ">=", $startDate);
        }
        if ($endDate) {
            $this->query->whereDate("{$this->baseTable}.created_at", "<=", $endDate);
        }

    }
}