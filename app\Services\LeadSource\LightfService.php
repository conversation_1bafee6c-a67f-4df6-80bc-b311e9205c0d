<?php

namespace App\Services\LeadSource;

use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;

class LightfService
{
    /**
     * Validate the request parameters.
     *
     * @param Request $request
     * @param array $requiredParams
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateRequestParameters(Request $request, array $requiredParams)
    {

        $validatedParams = [];
        $missingParams = [];

        foreach ($requiredParams as $param) {
            $value = $request->query($param);

            if (!$value) {
                $missingParams[] = $param;
                continue;
            }

            $validatedParams[$param] = $value;
        }

        if (!empty($missingParams)) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.missing_required_parameters'),
                'missing_params' => $missingParams,
            ], 400);
        }

        return response()->json([
            'response' => 'success',
            'result' => $validatedParams,
        ]);
    }


    /**
     * Get Access Token
     *
     * @param string $code
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAccessToken($code)
    {
        $clientId = env('LIGHTF_API_KEY');
        $clientSecret = env('LIGHTF_API_SECRET');
        $tokenUrl = 'https://api.lightfunnels.com/api/access_token';

        $credentials = base64_encode("{$clientId}:{$clientSecret}");

        $response = Http::withHeaders([
            'Authorization' => "Basic {$credentials}",
            'Content-Type' => 'application/x-www-form-urlencoded'
        ])->asForm()->post($tokenUrl, [
            'code' => $code
        ]);

        if ($response->failed()) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_get_access_token')
            ], $response->status());
        }

        $data = $response->json();

        if (!isset($data['access_token'])) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_get_access_token')
            ], 400);
        }

        $accessToken = $data['access_token'];
        session([env('LIGHTF_TOKEN_SESSION_KEY') => $accessToken]);

        return response()->json([
            'response' => 'success',
            'result' => $accessToken
        ]);
    }

    /**
     * Verify the webhook request.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|bool
     */
    public function verifyWebhook(Request $request)
{
    $appSecret = env('LIGHTF_API_SECRET');

    if (empty($appSecret)) {
        return response()->json([
            'response' => 'error',
            'message' => __('sourcing.request_not_authorized'),
        ], 401);
    }

    // Get the HMAC from headers
    $receivedHmac = $request->header('lightfunnels-hmac');

    // Calculate the expected HMAC (using raw binary output and then base64 encode it)
    $payload = $request->getContent();

    $calculatedHmac = base64_encode(hash_hmac('sha256', $payload, $appSecret, true));

    if (empty($receivedHmac) || !hash_equals($calculatedHmac, $receivedHmac)) {

        return response()->json([
            'response' => 'error',
            'message' => __('sourcing.request_not_authorized'),
        ], 401);
    }

    return true;
}

    /**
     * Format a single order for export.
     *
     * @param array $dataRow
     * @return array
     */
    public function formatWebHookOrder($dataRow)
    {
        // Extract product details from items and group by product_id
        $items = $dataRow['node']['items'] ?? [];
        $groupedItems = [];

        foreach ($items as $item) {
            $productId = $item['product_id'] ?? null;
            if (!$productId) {
            continue;
            }

            if (!isset($groupedItems[$productId])) {
            $groupedItems[$productId] = [
                "name"   => GlobalHelper::RemoveEmoji($item['title'] ?? ''),
                "skuNo"  => $item['sku'] ?? null,
                "skuQty" => 0,
            ];
            }

            $groupedItems[$productId]['skuQty']++;
        }

        $skuDetailList = array_values($groupedItems);

        // Return formatted order data
        return [[
            "storeName"          => "From LightFunnels",
            "orderCode"          => $dataRow['node']['name'] ?? null,
            "consigneeCountry"   => $dataRow['node']['shipping_address']['country'] ?? null,
            "consigneeContact"   => GlobalHelper::RemoveEmoji($dataRow['node']['shipping_address']['first_name'] . ' ' . $dataRow['node']['shipping_address']['last_name']),
            "consigneeMobile"    => GlobalHelper::RemoveEmoji($dataRow['node']['shipping_address']['phone'] ?? ''),
            "whatsappPhone"      => GlobalHelper::RemoveEmoji($dataRow['node']['shipping_address']['phone'] ?? ''),
            "consigneeArea"      => GlobalHelper::RemoveEmoji($dataRow['node']['shipping_address']['line1'] ?? ''),
            "consigneeCity"      => GlobalHelper::RemoveEmoji($dataRow['node']['shipping_address']['city'] ?? ''),
            "goodsDescription"   => implode(' / ', array_map(function ($groupedItem) {
                return GlobalHelper::RemoveEmoji($groupedItem['name'] ?? '');
            }, $skuDetailList)),
            "productVaritante"   => implode(' / ', array_map(function ($item) {
                $options = $item['options'] ?? [];
                return implode(', ', array_map(function ($option) {
                    return GlobalHelper::RemoveEmoji($option['label'] . ': ' . $option['value']);
                }, $options));
            }, $items)),
            "skuDetailList"      => $skuDetailList,
            "goodsValue"         => $dataRow['node']['total'] ?? null,
            "currency"           => $dataRow['node']['currency'] ?? null,
            "ProductLink"        => "",
            "comment_shipping"   => GlobalHelper::RemoveEmoji($dataRow['node']['shipping_address']['zip'] ?? ''),
            "note"               => GlobalHelper::RemoveEmoji($dataRow['node']['notes'] ?? ''),
            "orderSource"        => "lightfunnels",
        ]];
    }


}