<?php
namespace App\Services\Fields;

use App\Models\SellersStock;
use App\Services\Fields\FieldsService;
use App\Services\Orders\OrderFlowService;
use App\Services\Orders\OrderService;
use App\Services\Orders\OrderStatusService;
use GlobalHelper;
use SellersHelper;
use stdClass;

class FormatterOrders {

    protected $FieldsService;
    protected $OrderStatusService;
    protected $currentSeller;
    protected $OrderFlowService;
    protected $orderFlow;
    protected $OrderService;
    protected $orderProducts;
    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->OrderStatusService = new OrderStatusService();
        $this->OrderService = new OrderService();
        $this->OrderFlowService = new OrderFlowService();

        // Initialize orderFlow
        $this->orderFlow = new stdClass();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }
    
    
    /**
     * Format the orders based on provided options
     */
    public function formatterResult(array $options = []): array|null{
        // Extract options
        $first = $options['first'] ?? null;
        $items = $options['items'] ?? [];

        // Convert to array using GlobalHelper
        $items = GlobalHelper::ConvertToArray(['liste' => $items]);

        // Return empty array if no items are provided
        if (empty($items)) {
            return $first === 1 ? null : ['items' => []];
        }

        // Get Confirmation Noes
        $this->orderFlow->confirmationNotes = $this->getConfirmationNotes($items);
        $this->orderFlow->followupNotes = $this->getFollowupNotes($items);

        // Format the items
        $formattedItems = array_map(function ($row) {
            $groupedData = $this->FieldsService->GroupeFields('orders', $row);
            return $this->FormatterOrder($groupedData);
        }, $items);

        // Return either the first item or all formatted items
        return $first === 1 ? ($formattedItems[0] ?? null) : ['items' => $formattedItems];
    }

    /**
     * Formatter for an individual order
     */
    private function FormatterOrder(array $row_order): array{
        // Format General
        $row_order = $this->FormatterGeneral($row_order);
        
        // Format product
        $row_order = $this->FormatterProduct($row_order);

        // Format status
        $row_order = $this->FormatterStatus($row_order);

        // Format followup
        $row_order = $this->FormatterFollowup($row_order);

        // return order
        return $row_order;
    }

    /**
     * Formatter general infos order
     */
    private function FormatterGeneral(array $row_order): array{
        //shipping Company
        if (array_key_exists('shippingCompany', $row_order)) {
            $row_order['shippingCompany'] = $this->OrderService->shippingCompanyName($row_order['shippingCompany']);
        }
    
        return $row_order;
    }
    /**
     * Formatter for the product details within an order
     */
    private function FormatterProduct(array $row_order): array{
        $orderId = $row_order['id'];

        // Check if both 'goodsDescription' and 'sku' exist in the array
        if (array_key_exists('goodsDescription', $row_order) && array_key_exists('sku', $row_order)) {
            $row_order['goodsDescription'] = $row_order['goodsDescription'] ?: $row_order['sku'];
            unset($row_order['sku']); 
        }

        // Product Link
        if (array_key_exists('productLink', $row_order) && !$row_order['productLink']) {
            // Render First Product
            $this->renderOrdersProduct($row_order['id']);

            // Get Order Product
            $orderProduct = $this->orderProducts[$orderId]['firstProduct'] ??  null;
            $row_order['productLink'] = $orderProduct->product_link ?? null;
        }
    
        return $row_order;
    }


    /**
     * Formatter for the status details within an order
     */
    private function FormatterStatus(array $row_order): array{
        $orderId = $row_order['id'];

        // Check if both 'status'
        if (array_key_exists('status', $row_order)) {
            // build Status Description
            $statusDescription = $this->OrderStatusService->buildStatusDescription($row_order,$this->currentSeller);
            $row_order = GlobalHelper::arrayInsertAfter($row_order,"status",['statusDescription' => $statusDescription]);
            
            // Map Standard Status
            $row_order['status'] = $this->OrderStatusService->mapStandardStatuses([
                'originStatus' => $row_order['status'],
            ]);       
        }

        // Confirmation Note
        if (array_key_exists('unconfirmedReason', $row_order)) {
            $confirmationNote = $this->orderFlow->confirmationNotes[$orderId] ?? null;
            $row_order['unconfirmedReason'] = $confirmationNote->slug_reponse ?? null;
            $row_order['unconfirmedDescription'] = $confirmationNote->comment ?? null;
        }

        return $row_order;
    }

    /**
     * Formatter Followup Infos
     */
    private function FormatterFollowup(array $row_order): array{
        $orderId = $row_order['id'];

        // Followup
        if(isset($row_order['followup'])){
            if (array_key_exists('createdAt', $row_order['followup'])) {
                // Create Date
                $row_order['followup']['createdAt'] = $row_order['followup']['createdAt'] ?? $row_order['createdAt'];

                // Status Description
                $row_order['followup']['statusDescription'] = $this->OrderStatusService->buildFollowupStatus($row_order);  
            }

            // Folowup Notes
            if (array_key_exists('rejectReason', $row_order['followup'])) {
                $followupNotes = $this->orderFlow->followupNotes[$orderId] ?? null;
                $followupNote = $followupNotes[0] ?? null;

                
                $row_order['followup']['rejectReason'] = $followupNote ? ($followupNote['rejectreason'] ?: $followupNote['comment']) : null;
                $row_order['followup']['comment'] = $followupNote['comment']?? null;
            }
       }

        return $row_order;
    }
    
    /**
     * Get confirmation notes for the given items.
     */
    private function getConfirmationNotes(array $items): ?array {
        // Check if the first item contains a confirmation note
        $firstItem = $items[0] ?? null;
        
        // If no confirmation note exists, return null
        if (!$firstItem || !array_key_exists('unconfirmedReason', $firstItem)) {
            return null;
        }

        // Extract order IDs and fetch confirmation notes
        $orderIds = array_column($items, 'id');
        return $this->OrderFlowService->confirmationNotes([
            'orderIds' => $orderIds,
        ]);
    }

    /**
     * Get followup notes for the given items.
     */
    private function getFollowupNotes(array $items) {
        // Check if the first item contains a confirmation note
        $firstItem = $items[0] ?? null;
        
        // If no confirmation note exists, return null
        if (!$firstItem || !array_key_exists('followupRejectReason', $firstItem)) {
            return null;
        }

        // Extract order IDs and fetch confirmation notes
        $orderIds = array_column($items, 'id');
        return $this->OrderFlowService->followupNotes([
            'orderIds' => $orderIds,
        ]);
    }


    /**
     * render Orders Product
     */
    private function renderOrdersProduct($orderId){
        $firstProduct = $this->OrderFlowService->getProducts([
            "orderId" => $orderId,
            "first" => 1,
        ]);

        if($firstProduct->id ?? null){
            if($firstProduct->main_product_id){
                $rowProduct = SellersStock::find($firstProduct->main_product_id);
                $this->orderProducts[$orderId]['firstProduct'] = $rowProduct;
            }
        }
    }
}
