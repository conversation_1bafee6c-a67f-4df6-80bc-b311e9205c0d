<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::create('sourcing_requests', function (Blueprint $table) {
            $table->id();

            // Seller
            $table->unsignedBigInteger('seller_id');

            // Product Details
            $table->string('product_name');
            $table->string('arabic_name')->nullable();
            $table->text('product_description')->nullable();
            $table->string('product_url'); // AliExpress, etc.
            $table->string('product_image')->nullable();
            $table->string('currency')->nullable();
            $table->boolean('is_tested')->nullable();
            $table->text('notes')->nullable(); // Reason for declination, etc.
            $table->decimal('target_price', 10, 2);
            $table->integer('estimated_quantity');

            // Sourcing Details
            $table->unsignedBigInteger('sourcing_category_id');
            $table->unsignedBigInteger('sourcing_shipping_method_id');
            $table->decimal('unit_price', 10, 2)->nullable(); 
            $table->decimal('total_price', 10, 2)->nullable(); 

            // These should not have auto_increment or primary key
            $table->unsignedBigInteger('destination_country'); 
            $table->unsignedBigInteger('origin_country'); 

            $table->string('product_found_image')->nullable();
            $table->text('message')->nullable(); // From Adonis

            // Status and Metadata
            $table->string('status')->default('pending');
            $table->string('shipping_status')->default('');
            $table->string('payment_status')->default('');
            $table->string('reason_of_refuse')->default(''); 
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        Schema::dropIfExists('sourcing_requests');
    }
};
