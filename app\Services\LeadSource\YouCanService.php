<?php

namespace App\Services\LeadSource;

use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class YouCanService
{

    /**
     * Validate the request parameters.
     *
     * @param Request $request
     * @param array $requiredParams
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateRequestParameters(Request $request, array $requiredParams)
    {

        $validatedParams = [];
        $missingParams = [];

        foreach ($requiredParams as $param) {
            $value = $request->query($param);

            if (!$value) {
                $missingParams[] = $param;
                continue;
            }

            $validatedParams[$param] = $value;
        }

        if (!empty($missingParams)) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.missing_required_parameters'),
                'missing_params' => $missingParams,
            ], 400);
        }

        return response()->json([
            'response' => 'success',
            'result' => $validatedParams,
        ]);
    }


    /**
     * Validate the YouCan request.
     *
     * @param Request $request
     * @return bool
     */
    public function isValidYouCanSignature(Request $request)
    {
        $hmacHeader = $request->header('x-youcan-signature');
        $data = json_decode($request->getContent(), true);

        $expectedSignature = hash_hmac(
            'sha256', // Hashing Algorithm
            json_encode($data), // Response Payload
            env('YOUCAN_WEBHOOK_SECRET') // OAuth Client Secret Key
        );

        if(!hash_equals($expectedSignature, $hmacHeader)){
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.request_not_authorized'), // hacker possibility here
            ], 401);

        }
        else
        {
            return true;
        }

    }

    /**
     * Handle OAuth errors
     */
    public function handleOAuthError(Request $request)
    {
        if ($request->has('error')) {

            if ($request->get('error') === 'access_denied') {
                return response()->json([
                    'response' => 'error',
                    'message' => __('sourcing.you_canceled_the_request'),
                ], 400);
            }
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.missing_required_parameters'),
            ], 400);
        }
        return response()->json([
            'response' => 'success',
        ], 200);
    }


    /**
     * Check if the YouCan access token is present in the session.
     *
     * @return \Illuminate\Http\JsonResponse|string
     */
    public function checkYouCanToken()
    {
        $accessToken = session(env('YOUCAN_TOKEN_SESSION_KEY'));

        if (!$accessToken) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.unauthorized_missing_access_token'),
            ], 401);
        }

        return $accessToken;
    }

     /**
     * Get Access Token
     */
    public function getAccessToken($code)
    {
        $response = Http::asForm()->post('https://api.youcan.shop/oauth/token', [
            'grant_type'    => 'authorization_code',
            'client_id'     => env('YOUCAN_API_KEY'),
            'client_secret' => env('YOUCAN_API_SECRET'),
            'redirect_uri'  => env('YOUCAN_REDIRECT_URI'),
            'code'          => $code,
        ]);

        $data = $response->json();

        if (!isset($data['access_token'])) {
            return response()->json([
                'response' => 'error',
                'message' => __(key: 'sourcing.failed_to_get_access_token')
            ], 500);
        }

        session([env('YOUCAN_TOKEN_SESSION_KEY') => $data['access_token']]);

        return response()->json([
            'response' => 'success',
            'result' => $data['access_token']
        ]);
    }

    /**
     * Format a single order for export.
     *
     * @param array $dataRow
     * @return array
     */
    public function formatWebHookOrder($dataRow)
    {
        // Extract product details from variants
        $variants = $dataRow['variants'] ?? [];
        $skuDetailList = array_map(function ($variant) {
            return [
                "name"   => GlobalHelper::RemoveEmoji($variant['variant']['product']['name'] ?? ''),
                "skuNo"  => $variant['variant']['sku'] ?? null,
                "skuQty" => $variant['quantity'] ?? 1,
            ];
        }, $variants);

        // Return formatted order data
        return [[
            "storeName"          => "From YouCan",
            "orderCode"          => $dataRow['ref'] ?? null,
            "consigneeCountry"   => $dataRow['customer']['country'] ?? null,
            "consigneeContact"   => GlobalHelper::RemoveEmoji($dataRow['customer']['full_name'] ?? ''),
            "consigneeMobile"    => GlobalHelper::RemoveEmoji($dataRow['customer']['phone'] ?? ''),
            "whatsappPhone"      => GlobalHelper::RemoveEmoji($dataRow['customer']['phone'] ?? ''),
            "consigneeArea"      => GlobalHelper::RemoveEmoji(($dataRow['customer']['city'] ?? '') . ', ' . ($dataRow['customer']['country'] ?? '')),
            "consigneeCity"      => GlobalHelper::RemoveEmoji($dataRow['customer']['city'] ?? ''),
            "goodsDescription"   => implode(' / ', array_map(function ($variant) {
                return GlobalHelper::RemoveEmoji($variant['variant']['product']['name'] ?? '');
            }, $variants)),
            "productVaritante"   => implode(' / ', array_map(function ($variant) {
                return GlobalHelper::RemoveEmoji(implode(', ', $variant['variant']['options'] ?? []));
            }, $variants)),
            "skuDetailList"      => $skuDetailList,
            "goodsValue"         => $dataRow['total'] ?? null,
            "currency"           => $dataRow['currency'] ?? null,
            "ProductLink"        => '',
            "comment_shipping"   => GlobalHelper::RemoveEmoji($dataRow['shipping']['status_text'] ?? ''),
            "note"               => GlobalHelper::RemoveEmoji($dataRow['customer']['notes'] ?? ''),
            "orderSource"        => "youcan",
        ]];
    }
}