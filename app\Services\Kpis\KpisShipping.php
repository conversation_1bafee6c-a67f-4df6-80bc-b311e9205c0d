<?php
namespace App\Services\Kpis;
use App\Services\Orders\FilterOrders;
use GlobalHelper;

class KpisShipping {

    protected FilterOrders $filterOrders;
    
    public function __construct() {
        $this->filterOrders = new FilterOrders();
    }

    /**
     * Get Liste Statuts
     */
    public function shippingKpis($data) {
        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);

        // Fetch the totals based on the different statuses
        $totals = $this->calculateTotals($data);

        // Calculate the rates based on totals
        $rates = $this->calculateRates($totals);

        // Return the formatted final data containing all KPIs and their values
        return $this->formatFinalData($totals, $rates);
    }

    /**
     * Returns the base data used for filtering orders.
     */
    private function getBaseData($data) {
        return array_merge($data, [
            'excludedOrderTypes' => ['followup', 'firstmile'],
            'count' => 1
        ]);
    }

    /**
     * Calculates the totals for different shipping statuses.
     */
    private function calculateTotals($data) {
        // Confirmed Orders
        $dataConfirmed = array_merge($data, ['confirmed' => "yes"]);
        $totalConfirmed = (float) $this->filterOrders->getOrders($dataConfirmed);

        // Processing
        $dataProcessing = array_merge($data, ['listStatus' => ['processing']]);
        $totalProcessing = (float) $this->filterOrders->getOrders($dataProcessing);

        // Shipped
        $dataShipped = array_merge($data, ['listStatus' => ['delivered', 'return', 'intransit']]);
        $totalShipped = (float) $this->filterOrders->getOrders($dataShipped);

        // Delivered Order
        $dataDelivered = array_merge($data, ['status' => "delivered"]);
        $totalDelivered = (float) $this->filterOrders->getOrders($dataDelivered);

        // Return orders
        $dataReturn = array_merge($data, ['status' => "return"]);
        $totalReturn = (float) $this->filterOrders->getOrders($dataReturn);

        // In transit
        $totalInTransit = $totalShipped - $totalDelivered - $totalReturn;

        // Total finished
        $totalFinished = $totalDelivered + $totalReturn;

        return compact('totalConfirmed', 'totalShipped','totalProcessing', 'totalDelivered', 'totalReturn', 'totalInTransit', 'totalFinished');
    }

    /**
     * Calculates the rates for different shipping statuses.
     */
    private function calculateRates($totals) {
        $rateProcessing = GlobalHelper::calculRate(['total' => $totals['totalProcessing'], 'baseTotal' => $totals['totalConfirmed']]);
        $rateShipped = GlobalHelper::calculRate([ 'total' => $totals['totalShipped'], 'baseTotal' => $totals['totalConfirmed']]);
        $rateDelivered = GlobalHelper::calculRate(['total' => $totals['totalDelivered'], 'baseTotal' => $totals['totalFinished']]);
        $rateDeliveredReel = GlobalHelper::calculRate(['total' => $totals['totalDelivered'], 'baseTotal' => $totals['totalShipped']]);
        $rateReturn = GlobalHelper::calculRate(['total' => $totals['totalReturn'], 'baseTotal' => $totals['totalFinished']]);
        $rateReturnReel = GlobalHelper::calculRate(['total' => $totals['totalReturn'], 'baseTotal' => $totals['totalShipped']]);
        $rateInTransit = GlobalHelper::calculRate(['total' => $totals['totalInTransit'], 'baseTotal' => $totals['totalShipped']]);

        return compact('rateShipped', 'rateDelivered','rateProcessing', 'rateDeliveredReel', 'rateReturn', 'rateReturnReel', 'rateInTransit');
    }

    /**
     * Formats the final output data containing totals and calculated rates.
     */
    private function formatFinalData($totals, $rates) {
        return array_merge([
            'totalConfirmed' => $totals['totalConfirmed'],
            'rateConfirmed' => 100,
            'totalProcessing' => $totals['totalProcessing'],
            'rateProcessing' => $rates['rateProcessing'],
            'totalShipped' => $totals['totalShipped'],
            'rateShipped' => $rates['rateShipped'],
            'totalInTransit' => $totals['totalInTransit'],
            'rateInTransit' => $rates['rateInTransit'],
            'totalDelivered' => $totals['totalDelivered'],
            'rateDelivered' => $rates['rateDelivered'],
            'rateDeliveredReel' => $rates['rateDeliveredReel'],
            'totalReturn' => $totals['totalReturn'],
            'rateReturn' => $rates['rateReturn'],
            'rateReturnReel' => $rates['rateReturnReel'],
        ]);
    }
}
