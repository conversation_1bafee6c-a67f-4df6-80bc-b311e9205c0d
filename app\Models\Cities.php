<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Cities
 */
class Cities extends Model
{
    protected $table = 'cities';

    public $timestamps = true;

    protected $fillable = [
        'name',
        'codepostale',
        'region_id',
        'region_name',
        'grand_cities_id',
        'grand_cities_name',
        'agence_id',
        'agence_name',
        'livraison',
        'slugan',
        'tags',
        'parent',
        'type'
    ];

    protected $guarded = [];
    
    public function orders()
	{
		return $this->hasMany(SellersColis::class);
	}
        
}