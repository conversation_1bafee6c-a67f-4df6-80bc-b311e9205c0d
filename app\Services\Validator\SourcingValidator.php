<?php

namespace App\Services\Validator;

use App\Models\SourcingRequest;
use App\Models\SourcingProduct;

class SourcingValidator
{
    /**
     * Validate a sourcing request
     *
     * @param array $options
     * @return array
     */
    public function validateRequest($options = []): array
    {
        // Get params
        $id = $options['id'] ?? null;
        $layout = $options['layout'] ?? null;
        $status = $options['status'] ?? null;
        $with = $options['with'] ?? null;
        $sellerId = $options['seller_id'] ?? null;

        // Initial query
        $query = SourcingRequest::query();

        // Add conditions
        if ($id) {
            $query->where('id', $id);
        }

        if ($status) {
            $query->where('status', $status);
        }

        if ($sellerId) {
            $query->where('seller_id', $sellerId);
        }

        // Get first result
        $result = $query->first();

        // Check if exists
        if (!$result) {
            return [
                'response' => 'error',
                'message' => __('sourcing.request_not_found')
            ];
        }

        // Return success
        return [
            'response' => 'success',
            'result' => $result
        ];
    }

    /**
     * Validate a sourcing product
     *
     * @param array $options
     * @return array
     */
    public function validateProduct($options = []): array
    {
        // Get params
        $id = $options['id'] ?? null;
        $layout = $options['layout'] ?? null;
        $status = $options['status'] ?? null;
        $with = $options['with'] ?? null;

        // Initial query
        $query = SourcingProduct::query();

        // Add conditions
        if ($id) {
            $query->where('id', $id);
        }

        if ($status) {
            $query->where('status', $status);
        }

        // Get first result
        $result = $query->first();

        // Check if exists
        if (!$result) {
            return [
                'response' => 'error',
                'message' => __('sourcing.product_not_found')
            ];
        }

        // Return success
        return [
            'response' => 'success',
            'result' => $result
        ];
    }
}