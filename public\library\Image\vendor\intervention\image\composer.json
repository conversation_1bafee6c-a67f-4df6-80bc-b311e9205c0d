{"name": "intervention/image", "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["image", "gd", "imagick", "laravel", "watermark", "thumbnail"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://intervention.io/"}], "require": {"php": ">=5.4.0", "ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1 || ^2.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.7 || ^7.5.15", "mockery/mockery": "~0.9.2"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "minimum-stability": "stable"}