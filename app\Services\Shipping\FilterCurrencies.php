<?php
namespace App\Services\Shipping;

use App\Models\DeviseValues;
use App\Services\Fields\FieldsService;
use App\Traits\FilterQuery;

class FilterCurrencies {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $columnsType;
    use FilterQuery;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        
        // Table name
        $this->baseTable = 'devise_values';
        $this->columnsType = 'currencies';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getCurrencies(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Custom orders
        $this->data['ordering'] = $this->data['ordering'] ?? "asc";

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();
        
        // Fetch and return the final filtered results
        return $this->fetchResults();
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = DeviseValues::query();
    }
}