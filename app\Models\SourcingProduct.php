<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SourcingProduct extends Model
{
    protected $table = "seller_sourcing_product";

    public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
        'sourcing_request_id',
        'sourcing_request_code',
	    'arabic_name',
	    'attribute_names',
	    'reference',
	    'reference_code',
	    'weight',
	    'width',
	    'height',
	    'length',
	    'statut',
	    'is_archive',
	    'description_callcenter',
	    'product_link',
	    'product_video',
        'product_seller_image',
        'product_found_image',
	    'parent',
	    'total_products',
	    'warehouse_id',
	    'warehouse_name',
	    'shipping_by',
	    'confirmed_by',
	    'confirmed_at',
	    'created_by',
	    'updated_by',
	    'product_type',
	    'hscode',
	    'categorie_name',
	    'is_test',
        'process_mode',
        'origin_country',
        'destination_country',
	    'quantity',
        'shipping_method',
        'note',

        'name',
        'agreed_price'
	];

    public function variants()
    {
        return $this->hasMany(SourcingProductVariant::class, 'product_id');
    }
}