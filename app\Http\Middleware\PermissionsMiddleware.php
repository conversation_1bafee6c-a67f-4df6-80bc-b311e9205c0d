<?php
namespace App\Http\Middleware;
 
use Closure;
use Illuminate\Http\Request;
use RoleHelper;
use SellersHelper;
use Symfony\Component\HttpFoundation\Response;

class PermissionsMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, $permission){
        $currentSeller = SellersHelper::renderUserInfos();
    
        // Check if the current seller has the required permission
        if (!in_array($permission, $currentSeller->permissions ?? [])) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized.',
                'status'   => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }
    
        // Allow the request to proceed to the next middleware or request handler
        return $next($request);
    }
}
