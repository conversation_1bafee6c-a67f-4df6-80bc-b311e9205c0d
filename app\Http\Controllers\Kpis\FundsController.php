<?php

namespace App\Http\Controllers\Kpis;

use App\Http\Controllers\Controller;
use App\Services\Kpis\KpisFunds;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class FundsController extends Controller{
    protected KpisFunds $KpisFunds;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->KpisFunds = new KpisFunds();
    }

    /**
     * Funds Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function funds(Request $request): JsonResponse{
        // Validate the request
        $validateData = $this->validateData($request);
        if ($validateData['response'] === 'error') { return response()->json($validateData, 400); }

        $fundsKpis = $this->KpisFunds->{'funds' . ucfirst($request->type)}($request->all());
        
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $fundsKpis,
        ]);
    }

     /**
     * Validate
     */
    private function validateData(Request $request): ?array{
        // Validate the uploaded file using Laravel's Validator
        $validator = Validator::make($request->all(), [
            'type' => ['required', Rule::in(['all','confirmed','delivered'])]
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message'  => implode(", ", $validator->messages()->all()),
            ];
        }

        // Return success response if validation passes
        return [
            'response' => 'success',
        ];
    }
}