<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SourcingRequest;

class SourcingRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $sourcingRequests = [
            [
                'seller_id' => 1,
                'product_name' => 'Samsung S23 Ultra',
                'arabic_name' => 'سامسونج S23 الترا',
                'product_description' => 'Flagship smartphone with advanced camera features.',
                'product_url' => 'https://example.com/samsung-s23-ultra',
                'product_image' => 'samsung-s23-ultra.jpg',
                'currency' => 'USD',
                'is_tested' => true,
                'notes' => 'Urgent request for delivery by end of November.',
                'target_price' => 16543.00,
                'estimated_quantity' => 40,
                'sourcing_category_id' => 1, // Electronics
                'sourcing_shipping_method_id' => 1, // Air Freight
                'destination_country' => 1,
                'origin_country' => 2,
                'total_price' => 661720.00, // target_price * estimated_quantity
                'product_found_image' => 'samsung-s23-ultra-found.jpg',
                'message' => 'Product sourced successfully.',
                'status' => 'pending',
                'shipping_status' => 'pending',
                'payment_status' => 'pending',
                'reason_of_refuse' => '',
            ],
            [
                'seller_id' => 2,
                'product_name' => 'MacBook Pro M2',
                'arabic_name' => 'ماك بوك برو M2',
                'product_description' => 'High-performance laptop for professionals.',
                'product_url' => 'https://example.com/macbook-pro-m2',
                'product_image' => 'macbook-pro-m2.jpg',
                'currency' => 'USD',
                'is_tested' => false,
                'notes' => 'Ensure proper packaging for ocean freight.',
                'target_price' => 13456.00,
                'estimated_quantity' => 25,
                'sourcing_category_id' => 5, // Laptops
                'sourcing_shipping_method_id' => 2, // Ocean
                'destination_country' => 3,
                'origin_country' => 2,
                'total_price' => 336400.00, // target_price * estimated_quantity
                'product_found_image' => 'macbook-pro-m2-found.jpg',
                'message' => 'Product sourced successfully.',
                'status' => 'pending',
                'shipping_status' => 'pending',
                'payment_status' => 'pending',
                'reason_of_refuse' => '',
            ],
            [
                'seller_id' => 3,
                'product_name' => 'AirPods Max',
                'arabic_name' => 'ايربودز ماكس',
                'product_description' => 'Premium over-ear headphones with noise cancellation.',
                'product_url' => 'https://example.com/airpods-max',
                'product_image' => 'airpods-max.jpg',
                'currency' => 'USD',
                'is_tested' => true,
                'notes' => 'Ensure fast delivery for pre-orders.',
                'target_price' => 17890.00,
                'estimated_quantity' => 35,
                'sourcing_category_id' => 2, // Audio Devices
                'sourcing_shipping_method_id' => 1, // Air Freight
                'destination_country' => 1,
                'origin_country' => 2,
                'total_price' => 626150.00, // target_price * estimated_quantity
                'product_found_image' => 'airpods-max-found.jpg',
                'message' => 'Product sourced successfully.',
                'status' => 'pending',
                'shipping_status' => 'pending',
                'payment_status' => 'pending',
                'reason_of_refuse' => '',
            ],
        ];

        foreach ($sourcingRequests as $request) {
            SourcingRequest::create($request);
        }
    }
}
