<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersColisCalls extends Model
{
    //
     protected $table = 'sellers_colis_calls';

	public $timestamps = true;

	protected $fillable = [
	    'colis_id',
	    'colis_barecode',
	    'order_num',
	    'datecall',
	    'reponse',
	    'slug_reponse',
	    'comment',
	    'date_reported',
	    'time_reported',
	    'created_by',
	    'created_by_id',
	    'call_type',
	    'statut_call',
	    'suggestupsell',
	    'invoice_id',
	    'invoice_num',
	    'is_paid',
		'canal'
	];

	protected $guarded = []; 
}
    