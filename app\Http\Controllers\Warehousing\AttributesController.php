<?php

namespace App\Http\Controllers\Warehousing;

use App\Http\Controllers\Controller;
use App\Models\SellersStockAttributes;
use App\Services\Fields\FieldsService;
use Illuminate\Http\Request;
use SellersHelper;

class AttributesController extends Controller
{

    protected FieldsService $FieldsService;
    protected $currentSeller;
    protected $baseTable = "sellers_stock_attributes";

    public function __construct()
    {
        $this->FieldsService = new FieldsService();
        $this->currentSeller = SellersHelper::CurrentSeller();
    }
    // Display a listing of the resource
    public function list()
    {
        $attributes = SellersStockAttributes::where('seller_id', $this->currentSeller->id)->get();
        $mappedAttributes = $attributes->map(function ($attribute) {
            return [
                'id' => $attribute->id,
                'name' => $attribute->attribute_name,
                'value' => $attribute->attribute_values,
            ];
        });
        return response()->json([
            'response' => 'success',
            'result' => $mappedAttributes
        ], 201);
    }


    // Display a listing of the resource
    public function details($id){
        $verification = $this->verifyAttribute($id);
        if ($verification->getStatusCode() !== 200) {
            return $verification;
        }
        $attribute = $verification->getData()->result;

            return [
                'id' => $attribute->id,
                'name' => $attribute->attribute_name,
                'value' => $attribute->attribute_values,
            ];

    }

    // Store a newly created resource in storage
    public function store(Request $request)
    {

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'attributes',
            'option' => 'createValidation',
            'data' => $request->all(),
            'replaceParams' => [
                "{seller_id}" => $this->currentSeller->id
            ]
        ]);
        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}



        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'store',
            'type' => 'attributes',
            'data' => $request->all(),
            'AppendParams' => [
                'seller_id' =>  $this->currentSeller->id,
                'seller_name' =>  $this->currentSeller->fullname,
            ]
        ]);

        // // Check if any existing attribute has the same name
        // $existingAttribute = SellersStockAttributes::where('seller_id', $this->currentSeller->id)
        //     ->where('attribute_name', $processData['attribute_name'])
        //     ->first();

        // if ($existingAttribute) {
        //     return response()->json([
        //         'response' => 'error',
        //         'message' => __('products.attribute_name_already_exists'),
        //     ], 400);
        // }

        // Create the attribute
        $attribute = SellersStockAttributes::create($processData);

        //get attribute details
        $attributeDetails = $this->details($attribute->id);

        // Return the response with the created attribute and its details
        return response()->json([
             'response' => 'success',
             'message' => __('products.attribute_created_successfully'),
             'result' => $attributeDetails
         ], 201);
    }

    // Display the specified resource
    public function show($id)
    {
       // Verify if the attribute belongs to the current seller
       $verification = $this->verifyAttribute($id);
       if ($verification->getStatusCode() !== 200) {
           return $verification;
       }
        //get attribute details
        $attributeDetails = $this->details($id);

        // Return the response with the created attribute and its details
        return response()->json([
             'response' => 'success',
             'result' => $attributeDetails
         ], 201);
    }

    // Update the specified resource in storage
    public function update(Request $request, $id)
    {

        // Verify if the attribute belongs to the current seller
        $verification = $this->verifyAttribute($id);
        if ($verification->getStatusCode() !== 200) {
            return $verification;
        }
        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'attributes',
            'option' => 'updateValidation',
            'data' => $request->all(),
            'replaceParams' => [
                "{id}" => $id,
                "{seller_id}" => $this->currentSeller->id
            ]
        ]);
        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'update',
            'type' => 'attributes',
            'data' => $request->all(),
        ]);


        // // Check if any existing attribute has the same name
        // $existingAttribute = SellersStockAttributes::where('seller_id', $this->currentSeller->id)
        // ->where('attribute_name', $processData['attribute_name'])
        // ->first();

        // if ($existingAttribute) {
        // return response()->json([
        //     'response' => 'error',
        //     'message' => __('products.attribute_name_already_exists'),
        // ], 400);
        // }
        // Verify if the attribute belongs to the current seller

        $attribute = SellersStockAttributes::find($id);
        $attribute->update($processData);

        $attributeDetails = $this->details($id);
        // Return the response with the updated offer and its prices
        return response()->json([
            'response' => 'success',
            'message' => __('products.offer_updated_successfully'),
            'result' => $attributeDetails,
        ]);
    }

    // Remove the specified resource from storage
    public function destroy($id)
    {
        if (is_null($id)) {
            return response()->json([
            'response' => 'error',
            'message' => __('products.invalid_attribute_id'),
            ], 400);
        }
        // Verify if the attribute belongs to the current seller
        $verification = $this->verifyAttribute($id);
        if ($verification->getStatusCode() !== 200) {
            return $verification;
        }
        $attribute = SellersStockAttributes::find($id);

        $attribute->delete();
        return response()->json([
            'response' => 'success',
            'message' => __('products.attribute_deleted_successfully'),
        ]);
    }



    // Verify if the attribute belongs to the current seller
    private function verifyAttribute($attributeId)
    {
        if (is_null($attributeId)) {
            return response()->json(['response' => 'error', 'message' => __('products.invalid_attribute_id')], 400);

        }
        $attribute = SellersStockAttributes::find($attributeId);

        if (!$attribute) {
            return response()->json(['response' => 'error', 'message' => __('products.attribute_not_found')], 404);
        }
        if (!$attribute || $attribute->seller_id !== $this->currentSeller->id) {
            return response()->json(['response' => 'error', 'message' => __('sourcing.unauthorized_missing_access_token')], 403);
        }

        return response()->json(['response' => 'success', 'result' => $attribute], 200);
    }
}