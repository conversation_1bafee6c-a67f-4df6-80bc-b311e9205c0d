<?php

namespace App\Http\Controllers\Warehousing;

use App\Http\Controllers\Controller;
use App\Models\SellersStockOffers;
use App\Models\SellersStockOffersPrices;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\Fields\FieldsService;
use App\Services\Validator\ProductsValidator;
use SellersHelper;

class ProductsOfferController extends Controller{
    protected ProductsValidator $productsValidator;
    protected FieldsService $FieldsService;
    protected ProductsController $productsController;
    protected $currentSeller;

    public function __construct(){
        $this->productsValidator = new ProductsValidator();
        $this->FieldsService = new FieldsService();
        $this->productsController = new ProductsController();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }


    /**
     * Create a new product offer.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request, $productId): JsonResponse
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'productsSalesPrices',
            'option' => 'createValidation',
            'data' => $request->all(),

        ]);

        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'store',
            'type' => 'productsSalesPrices',
            'data' => $request->all(),
            'AppendParams' => [
                'seller_id' =>  $this->currentSeller->id,
                'seller_name' =>  $this->currentSeller->fullname,
                'product_name' => $validateProduct['rowProduct']->name,
                'product_id' => $productId,
            ]

        ]);

        // Create the offer
        $offer = SellersStockOffers::create($processData);

        // add offer Prices
        if ($request->has('prices')) {
            $this->addOfferPrices($request->prices, $productId, $validateProduct['rowProduct']->name, $offer);
        }

        //get product details
        $productDetails = $this->productDetails($request,$productId);

        // Return the response with the new offer and its prices
        return response()->json([
            'response' => 'success',
            'message' => __('products.offer_created_successfully'),
            'result' => $productDetails,
        ], 201);
    }


    /**
     * update a product offer.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request, $productId, $offerId)
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate the offer
        $validationResult = $this->productsValidator->validateOffer($productId, $offerId);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 404);
        }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'productsSalesPrices',
            'option' => 'updateValidation',
            'data' => $request->all(),
        ]);
        if ($resultValidator['response'] == "error") {
            return response()->json($resultValidator, 400);
        }

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'update',
            'type' => 'productsSalesPrices',
            'data' => $request->all(),
        ]);

        // Update the offer
        $offer = $validationResult['offer'];
        $offer->update($processData);

        // add offer Prices
        if ($request->has('prices')) {
            $this->addOfferPrices($request->prices, $productId, $validateProduct['rowProduct']->name, $offer, true);
        }

        //get product details
        $productDetails = $this->productDetails($request,$productId);

        // Return the response with the updated offer and its prices
        return response()->json([
            'response' => 'success',
            'message' => __('products.offer_updated_successfully'),
            'result' => $productDetails,
        ]);

    }

    /**
     * Delete a product offer.
     *
     * @param int $productId
     * @param int $offerId
     * @return JsonResponse
     */
    public function destroy($productId, $offerId)
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId, 'layout' => 'details', "with" => ["salesPrices.prices", "upsell.prices"]]);
        if ($validateProduct['response'] == 'error') {
            return response()->json($validateProduct, 404);
        }

        // Validate the offer
        $validationResult = $this->productsValidator->validateOffer($productId, $offerId);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 404);
        }

        // Delete the offer prices
        SellersStockOffersPrices::where('offer_id', $offerId)->delete();

        // Delete the offer
        $validationResult['offer']->delete();

        // Return the response
        return response()->json([
            'response' => 'success',
            'message' => __('products.offer_deleted_successfully'),
        ]);
    }


    /**
     * Add prices for a product offer.
     *
     * @param array $prices
     * @param int $productId
     * @param string $productName
     * @param SellersStockoffers $offer
     * @param bool $deleteBefore
     * @return void
     */
    private function addOfferPrices(array $prices, int $productId, string $productName, SellersStockoffers $offer, bool $deleteBefore = false)
    {

        if(empty($prices)) {
            return;
        }


        if ($deleteBefore) {
            // Delete existing prices for the offer
            SellersStockoffersPrices::where('offer_id', $offer->id)->delete();
        }


        foreach ($prices as $price) {
            SellersStockOffersPrices::create([
            'product_id' => $productId,
            'product_name' => $productName,
            'offer_id' => $offer->id,
            'offer_name' => $offer->offer_name,
            'currency' => $price['currency'],
            'price' => $price['price'],

            ]);
        }



        // Update the offer price_description column
         $offer->price_description = $prices[0]['price'] . ' ' . $prices[0]['currency'];
        $offer->save();

    }

    /**
     * Get product details from products controller
     */
    private function productDetails($request,$id){
        // Call the details method (for product)
        $detailsResponse = $this->productsController->details($request, $id);

        // Extract the data from the JsonResponse object
        return $detailsResponse->getData(true)['result'];
    }


}