<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SourcingInvoices extends Model
{
    //
     protected $table = 'sourcing_invoices';

	public $timestamps = true;

	protected $fillable = [
	    'date',
	    'code',
	    'invoice_num',
	    'seller_id',
	    'seller_name',
	    'store_name',
	    'sourcing_type',
	    'product_id',
	    'product_name',
	    'product_link',
	    'product_quantity',
	    'rmb_tousd',
	    'risk_exhange',
	    'exhange_price',
	    'supplier_unit_price',
	    'supplier_unit_price_usd',
	    'supplier_total_price',
	    'supplier_total_price_usd',
	    'sourcing_margin',
	    'total_sourcing_margin',
	    'client_unit_price',
	    'client_total_price',
	    'price_shipping_provider',
	    'price_shipping_margin',
	    'total_shipping_margin',
	    'total_shipping_client',
	    'total_to_pay',
	    'total_cost_provider',
	    'total_result',
	    'paid_customer_statut',
	    'paid_customer_date',
	    'paid_sourcing_statut',
	    'paid_sourcing_date',
	    'paid_shipping_statut',
	    'paid_shipping_date',
	    'statut_validate',
	    'statut',
	    'created_by',
	    'update_by',
	    'deleted_by',
	];

	protected $guarded = []; 
}
    