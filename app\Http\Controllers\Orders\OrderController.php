<?php

namespace App\Http\Controllers\Orders;

use App\Http\Controllers\Controller;
use App\Services\Orders\OrderStatusService;
use App\Services\Orders\FilterOrders;
use App\Services\Orders\OrderFlowService;
use App\Services\Orders\OrderService;
use App\Services\Validator\OrdersValidator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class OrderController extends Controller{
    protected FilterOrders $filterOrders;
    protected OrderStatusService $OrderStatusService;
    protected OrdersValidator $OrdersValidator;
    protected OrderService $OrderService;
    protected OrderFlowService $OrderFlowService;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->filterOrders = new FilterOrders();
        $this->OrderStatusService = new OrderStatusService();
        $this->OrdersValidator = new OrdersValidator();
        $this->OrderService = new OrderService();
        $this->OrderFlowService = new OrderFlowService();
    }

    /**
     * Retrieve and return a list of filtered orders.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse{
        // Retrieve the list of orders based on the provided filters
        $result = $this->filterOrders->getOrders($request->all());

        // Prepare the base response
        $results = [
            'response' => 'success',
            'result' => $request->count ? $result : ($result['items'] ?? []),
        ];

        // Include pagination details only if count is not requested
        if (!$request->count) {
            $results['paginate'] = [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ];
        }

        // Return the response as a JSON object
        return response()->json($results);

    }

    /**
     * Order details
     */
    public function details(Request $request,$id){
        // Validate Product
        $validateOrder = $this->OrdersValidator->validateOrder(['idOrderCode' => $id,'layout' => 'general,details']);
        if($validateOrder['response'] == 'error'){ return response()->json($validateOrder, 404); }

        // Get Product
        $rowOrder = $validateOrder["rowOrder"] ?? [];

        // Get Counts
        $countProducts = $this->OrderFlowService->getProducts(['orderId' => $rowOrder->id, 'sumQty' => 1]);
        $countCalls = $this->OrderFlowService->getOrderCalls(['orderId' => $rowOrder->id, 'count' => 1]);
        $countConfirmations = $this->OrderFlowService->getOrderConfirmation(['orderId' => $rowOrder->id, 'type' => 'confirmation', 'count' => 1]);
        $countShipping = $this->OrderFlowService->getOrderConfirmation(['orderId' => $rowOrder->id, 'type' => 'shipping', 'count' => 1]);
        $countFollowup = $this->OrderFlowService->getOrderFollowup(['orderId' => $rowOrder->id, 'count' => 1]);

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $rowOrder,
            'countProducts' => $countProducts,
            'countCalls' => $countCalls,
            'countConfirmations' => $countConfirmations,
            'countShipping' => $countShipping,
            'countFollowup' => $countFollowup,
        ]);
    }

    /**
     * List of status
     */
    public function statuses(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->OrderStatusService->mapStandardStatuses([
                "getList" => 1,
            ]),
            'orderStatusesInMenu' => ['newlead','confirmed','processing','intransit','delivered','return'],
            'notRealStatus' => ['newlead','wrongphonenumber','test','doubleorder','pending'],
            'shipping' => ["intransit","delivered","return"],
        ]);
    }

    /**
     * List of status
     */
    public function followupstatuses(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->OrderStatusService->followupStatusList([
                "getList" => 1,
            ]),
            'followupStatusesInMenu' => ['new','delivery','rtc','no-answer'],
        ]);
    }

    /**
     * List of payments methods
     */
    public function payments(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->OrderStatusService->mapPaymentsMethods(),
        ]);
    }
}