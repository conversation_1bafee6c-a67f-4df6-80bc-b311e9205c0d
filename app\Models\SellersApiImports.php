<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersApiImports extends Model
{
    //
     protected $table = 'sellers_api_imports';

	public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
	    'api_label',
	    'api_name',
	    'api_token',
	    'api_key',
	    'api_secretkey',
	    'shopurl',
	    'webhook',
	    'last_imported_order',
	    'last_imported_date',
	    'created_by_user_id',
	];

	protected $guarded = []; 
}
    