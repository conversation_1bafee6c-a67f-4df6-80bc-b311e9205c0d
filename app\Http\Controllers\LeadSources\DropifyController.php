<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\SellersMetadata;
use App\Services\LeadSource\DropifyService;
use App\Services\LeadSource\LeadSourceService;
use App\Services\Orders\OrderAPIService;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Http;
use SellersHelper as GlobalSellersHelper;
class DropifyController extends Controller
{
    protected $dropifyService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->dropifyService = new DropifyService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }

    /**
     * Validate if the request parameters are from Dropify.
     */
    public function isDropifyRequest(Request $request)
    {
        $validationResponse = $this->dropifyService->validateRequestParameters($request, ['client_id','redirect_uri']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        $isValid = $this->dropifyService->isValidDropifySignature($request->all());

        if ($isValid !== true) {
            return $isValid;
        }

        return response()->json([
            'response' => 'success',
        ],200);
    }
    /**
     * Install Dropify - Redirect to authorization page.
     */
    public function authorize(Request $request)
    {

        $validationResponse = $this->dropifyService->validateRequestParameters($request, ['client_id','redirect_uri']);
        $data = $validationResponse->getData(true);


        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }

        $client_id = $data['result']['client_id'];
        $redirect_uri = $data['result']['redirect_uri'];
        $request_from_dropify = $this->dropifyService->isValidDropifySignature($request->all());

        if ($request_from_dropify !== true) {
            return $request_from_dropify; // HACKER possibility here
        }

        return response()->json([
            'response' => 'success',
            'result' => [
                'client_id' => $client_id,
                'redirect_uri' => $redirect_uri]

        ],200);

    }

    /**
     * Handle OAuth Callback - Get Access Token and Redirect.
     */
    public function install(Request $request)
    {
        $validationResponse = $this->dropifyService->validateRequestParameters($request, ['client_id','redirect_uri']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        $request_from_dropify = $this->dropifyService->isValidDropifySignature($request->all(),true);

        if ($request_from_dropify !== true) {
            return $request_from_dropify; // HACKER possibility here
        }

        $token = $this->CheckToken();

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'dropify',
            'api_token' => $token,
            'seller' =>  $this->currentSeller,
            'api_label' => 'Dropify',
            'shopurl' => 'Dropify',
        ]);


        $redirect_uri = base64_decode($data['result']['redirect_uri']);

         return response()->json([
            'response' => 'success',
            'result' => [
                'redirect_uri' => $redirect_uri . '?token=' . $token]

        ],200);

    }
    /**
     * Handle OAuth Callback - Get Access Token and Redirect.
     */
    public function setOrders(Request $request)
    {


        // Retrieve seller_id that was added in the middleware
        $sellerId = $request->get('seller_id');

        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => array_merge($request->all(), [
                'orderSource' => 'dropify',
            ]),
            'sellerId' => $sellerId,
        ]);

        // Check if the response contains an error
        if ($responseOrders['response'] === 'error') {
            return response()->json($responseOrders, 400);
        }
        // Return Response
        return response()->json($responseOrders, 200 );
    }
    private function CheckToken(){

        $sellersMetadata = SellersMetadata::where('seller_id', $this->currentSeller->id)
        ->where("meta_data", 'dropify_seller_token')->first();

        // check if already have a token
        if ($sellersMetadata && $sellersMetadata->meta_value) {
            return $sellersMetadata->meta_value;
        } else {
            //Create new token
            $token = GlobalHelper::generateToken();
            SellersMetadata::create([
                'seller_id' => $this->currentSeller->id,
                'meta_data' => 'dropify_seller_token',
                'meta_value' => $token,
            ]);
            return $token;
        }
    }

}