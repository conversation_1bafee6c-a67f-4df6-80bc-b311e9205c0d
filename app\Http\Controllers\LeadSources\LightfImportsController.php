<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\LightfService;
use App\Services\Orders\OrderAPIService;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use SellersHelper as GlobalSellersHelper;

class LightfImportsController extends Controller
{
    protected $lightfService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->lightfService = new LightfService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }

    /**
     * Install LightFunnels App - Redirect to authorization page.
     */
    public function install(Request $request){
        // Generate a random state and store it in session for verification later



        $redirectUri = env('LIGHTF_REDIRECT_URI');
        $scopes = "orders";

        $state = encrypt($this->currentSeller->id);
        Session::put('oauth_lightf_state', $state); // Store in session
        $installUrl = "https://app.lightfunnels.com/admin/oauth?"
            ."client_id=" . env('LIGHTF_API_KEY')
            ."&redirect_uri={$redirectUri}"
            ."&response_type=code"
            ."&scope={$scopes}"
            ."&state={$state}"; // Use the generated state


            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ], 200);
    }

    /**
     * Save the state in session
     */
    public function saveState(Request $request){

        // Validate request parameters
        $validationResponse = $this->lightfService->validateRequestParameters($request, ['cod_state']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        // Remove the old session value if it exists and replace it with the new one
        Session::put('oauth_lightf_state' , $data['result']['cod_state']);

        return response()->json(['response'=>'success',
                         'message'=> __('sourcing.state_saved')], 200);
    }


    /**
     * Handle OAuth Callback - Get Access Token
     */
    public function callback(Request $request)
    {
        // Validate request parameters
        $validationResponse = $this->lightfService->validateRequestParameters($request, ['code','state']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        $code = $data['result']['code'];
        $state = $data['result']['state'];

        $tokenService = $this->lightfService->getAccessToken($code);

        if ($tokenService->getData()->response === 'error') {
            return $tokenService; // Return the error response
        }

        $accessToken = $tokenService->getData()->result; // Extract the token from the response

        //Get seller
        $seller = Sellers::find(decrypt($state));

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'LightFunnels',
            'api_token' => $accessToken,
            'seller' => $seller,
            // 'api_label' => $shop,
            // 'shopurl' => $shop,
        ]);
        // Register webhook
        $webhookUrl = route('lightf.webhook', ['id' => decrypt($state)]); // Make sure you have this route defined

        $webhookResponse = $this->registerWebhook($accessToken, $webhookUrl);

        if ($webhookResponse['response'] === 'error') {
            // Handle webhook registration error if needed
            // You might want to log this but not necessarily fail the entire process
        }

        return redirect()->away(env('URL_APP_SELLERS').'/lead-sources');


    }
    /**
     * Register webhook with Lightfunnels
     */
    protected function registerWebhook($accessToken, $webhookUrl)
    {
        $graphqlQuery = [
            'query' => '
                mutation CreateWebhookMutation($node: WebhookInput!) {
                    createWebhook(node: $node) {
                        type
                        settings
                        url
                    }
                }
            ',
            'variables' => [
                'node' => [
                    'type' => 'order/created',
                    'url' => $webhookUrl,
                    'settings' => new \stdClass(), // Empty object
                ]
            ]
        ];

        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->post('https://api.lightfunnels.com/graphql', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                ],
                'body' => json_encode($graphqlQuery),
            ]);

            $responseData = json_decode($response->getBody(), true);

            return [
                'response' => 'success',
                'result' => $responseData['data']['createWebhook'] ?? null
            ];
        } catch (\Exception $e) {
            return [
                'response' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    public function handleWebhook(Request $request,$id)
    {
        $request_from_lightf = $this->lightfService->verifyWebhook($request);
        // save it same why as excel order
        if ($request_from_lightf !== true) {

            return $request_from_lightf; // HACKER possibility here
        }
        // Formatter Data
        $orderData = $this->lightfService->formatWebHookOrder($request->all());

        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $orderData,
            'sellerId' => $id
        ]);
        // save it same why as excel order

 // Check if the response contains an error
 if ($responseOrders['response'] === 'error') {
    Log::error('Order API Service returned an error', [
        'seller_id' => $id,
        'order_data' => $orderData,
        'response' => $responseOrders
    ]);
    return response()->json($responseOrders, 400);
}
// Return Response
return response()->json($responseOrders, 200 );
    }


    /**
     * Retrieve store information from Lightfunnels API
     *
     * @param string $storeId The Lightfunnels store ID
     * @param string $accessToken Your Lightfunnels API access token
     * @return array|null Returns store data or null on failure
     */
    protected function getLightfunnelsStore($storeId, $accessToken)
    {
        $apiUrl = 'https://services.lightfunnels.com/api/v2';

        $query = '
            query StoreQuery($id: ID!) {
                node(id: $id) {
                    ... on Store {
                        id
                        name
                    }
                }
            }
        ';

        $variables = [
            'id' => $storeId
        ];

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $accessToken,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($apiUrl, [
                'query' => $query,
                'variables' => $variables
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['data']['node'] ?? null;
            }

            Log::error('Lightfunnels API error', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);
            return null;

        } catch (\Exception $e) {
            Log::error('Lightfunnels API request failed', [
                'error' => $e->getMessage(),
                'store_id' => $storeId
            ]);
            return null;
        }
    }


}