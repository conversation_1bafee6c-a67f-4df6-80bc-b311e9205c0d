<?php
namespace App\Services\Orders;

use App\Models\SellersColis;
use App\Models\SellersColisActions;
use App\Models\SellersColisCalls;
use App\Models\SellersColisFollowupHistory;
use App\Models\SellersColisProducts;
use Illuminate\Support\Facades\DB;

class OrderFlowService{
    /**
     * Order Calls
     * @return array
     */
    public function getOrderCalls(array $data = []){
        // Get 'orderId' and 'count' from input, defaulting to null
        $orderId = $data['orderId'] ?? null;
        $count = $data['count'] ?? null;

        // Fetch calls for the given 'orderId', ordered by latest date
        $query = SellersColisCalls::where('colis_id', $orderId)
            ->orderBy('datecall', 'DESC');

        // Return Count
        if ($count) {
            return $query->count();
        }

        // Return Result
        return $query->get();
    }

    /**
     * Order confirmation
     * @return array
     */
    public function getOrderConfirmation(array $data = []){
        // Get 'orderId' and 'count' from input, defaulting to null
        $orderId = $data['orderId'] ?? null;
        $count = $data['count'] ?? null;
        $type = $data['type'] ?? null;

        // Fetch calls for the given 'orderId', ordered by latest date
        $query = SellersColisActions::where('colis_id', $orderId)
            ->orderBy('created_at', 'DESC');

        // Confirmation
        if($type == "confirmation"){ $query->whereIn('new_statut',$this->ConfirmationStatus()); }

        // Shipping
        if($type == "shipping"){ $query->whereNotIn('new_statut',$this->ConfirmationStatus()); }

         // Return Count
         if ($count) {
            return $query->count();
        }

        // Return Result
        return $query->get();
    }

    /**
     * Order Followup
     * @return array
     */
    public function getOrderFollowup(array $data = []){
        // Get 'orderId' and 'count' from input, defaulting to null
        $orderId = $data['orderId'] ?? null;
        $count = $data['count'] ?? null;

        // Fetch calls for the given 'orderId', ordered by latest date
        $query = SellersColisFollowupHistory::where('colis_id', $orderId)->whereNotNull('feedbacktype')
            ->orderBy('action_date','desc')->orderBy('id','desc');


        // Return Count
        if ($count) {
            return $query->count();
        }
        
        // Return Result
        return $query->get();
    }

    /**
     * get Confirmation Status
     */
    private function ConfirmationStatus(){
        return ["create-colis","edit-colis","no-answer","reported","picked-up","canceled","failed","test","duplicate-order","waiting-pick-up","expired","pending","change-agent","set-priority","remitted"];
    }

    /**
     * get confirmation Notes
     */
    public function confirmationNotes($options = []) {
        // Check if orderIds are provided and not empty
        $orderIds = $options['orderIds'] ?? [];
        if (!empty($orderIds)) {
            // Sanitize and prepare orderIds for use in the query
            $id_implode = implode(',', array_map(fn($id) => '"' . $id . '"', $orderIds));
    
            // Create the query to fetch the latest seller colis calls
            $query = "
                SELECT fh1.id,
                       fh1.colis_id,
                       fh1.colis_barecode,
                       fh1.comment,
                       fh1.reponse,
                       fh1.slug_reponse
                FROM sellers_colis_calls fh1
                JOIN (
                    SELECT fh2.colis_id, MAX(fh2.id) AS max_id
                    FROM sellers_colis_calls fh2
                    WHERE fh2.colis_id IN ($id_implode)
                    GROUP BY fh2.colis_id
                ) max_colis
                ON fh1.id = max_colis.max_id
            ";
    
            // Execute the query and fetch the results
            $history_notes = DB::select($query);
    
            // Format the results into an associative array by colis_id
            if ($history_notes) {
                $result_notes = [];
                foreach ($history_notes as $row_notes) {
                    $result_notes[$row_notes->colis_id] = $row_notes;
                }
                return $result_notes;
            }
        }
    
        return [];
    }

    /**
     * Get Followup Notes
     */
    function followupNotes($options = []) {
        // Check if orderIds are provided and not empty
        $orderIds = $options['orderIds'] ?? [];
        if (!empty($orderIds)) {
            return SellersColisFollowupHistory::whereIn('colis_id', $orderIds)
            ->where('action_name', 'submit_followup')
            ->orderBy('colis_id')
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy('colis_id')
            ->map(fn($items) => $items->toArray())
            ->toArray(); 
        }
    }

     /**
     * Fetch order products
     */
    public function getProducts(array $options = []): mixed{
        // Extract options with default values
        $orderId = $options['orderId'] ?? null;
        $productId = $options['productId'] ?? null;
        $status = $options['status'] ?? null;
        $count = $options['count'] ?? null;
        $sumQty = $options['sumQty'] ?? null;
        $first = $options['first'] ?? null;

        // Query to get products for the given order ID
        $query = SellersColisProducts::where('colis_id', $orderId);
       
        // Custom Conditions
        if($orderId){ $query->where('colis_id',$orderId); }
        if($productId){ $query->where('product_id',$productId); }

        // By Status
        if($status){ 
            if ($status == "inprogress") {
                $query->where(function ($q) {
                    $q->whereIn('statut_product', ['picked-up', 'in-transit', 'shipped']);
                    $q->OrWhere(function ($q) {
                        $q->whereIn('statut_product', ['undelivered']);
                    });
                });
            } elseif ($status) {
                $query->where('statut_product', $status);
            }
        }

        // Return Result
        if ($sumQty === 1) return $query->sum('quantity');
        if ($first) return $query->first();
        if ($count === 1) return $query->count();

        // Otherwise, return the list of products
        if($orderId){
            $orderCurrency = SellersColis::where('id', $orderId)->value('currency');
            return $query->get()->map(function ($product) use ($orderCurrency) {
                $product->colis_curr = $orderCurrency;
                return $product;
            });

        }else{
            return $query->get();
        }
    }
}