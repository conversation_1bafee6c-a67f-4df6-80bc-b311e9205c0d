<?php
namespace App\Services\Orders;

use GlobalHelper;
use SellersHelper;

class OrderStatusService{

    protected $callcenterStatus;
    protected $currentSeller;
    protected $order;
    protected $statusDescription;

    /**
     * Construct
     */
    public function __construct() {
        $this->callcenterStatus = $this->getCallCenterStatuses(['arKeys' => 1]);
    }

    /*
     * Main function to format the order status
     */
    public function buildStatusDescription($order, $currentSeller) {
        $this->currentSeller = $currentSeller;

        // Convert to object if the order is an array
        $this->order = $this->ensureOrderIsObject($order);

        // Remove Slugan if the user doesn't have permission
        $this->removeSluganIfNoPermission();

        // Apply status transformation
        $this->applyStatusTransformation();

        // Return the order as the original type (array or object)
        return $this->statusDescription;
    }

    /**
     * build Followup Status
     */
    public function buildFollowupStatus($order) {
        $statusDescription = "";

        // Convert to object if the order is an array
        $order = $this->ensureOrderIsObject($order);

        // Check if order and followup status exist
        if ($order && isset($order->followup->status)) {
            // Get status description using the status code
            $statusList = $this->followupStatusList();
            $statusDescription = $statusList[$order->followup->status] ?? '';

            // Convert schedule date if it exists
            $scheduleDate = GlobalHelper::convertDate($order->followup->scheduleDate);

            // Append schedule date if available
            if ($scheduleDate) {
                $statusDescription .= "<br>" . $scheduleDate;
            }
        }

        return $statusDescription;
    }


    /*
     * Remove Slugan if the user does not have shipping status permission
     */
    private function removeSluganIfNoPermission() {
        if (!SellersHelper::CheckPermission($this->currentSeller,"shipping.status") && !in_array($this->order->status, $this->callcenterStatus)) {
            $this->order->moreInfo = "";
        }
    }

    /*
     * Apply status transformation based on the order status
     */
    private function applyStatusTransformation() {
        // Dynamic Status
        $DynamicStatus = $this->mapDynamicStatuses([
            'originStatus' => $this->order->status,
        ]);

        // Liste of status maps
        switch ($this->order->status) {
            case 'in-transit':
                $this->statusDescription = $this->order->shipping->shippedAt
                ? $DynamicStatus . "<br>" . GlobalHelper::convertDate($this->order->shipping->shippedAt)
                : $DynamicStatus;
                break;

            case 'shipped':
                $this->statusDescription = ($this->order->statusReason && strtolower($this->order->statusReason) != "followup")
                ? ($this->order->moreInfo ? : $DynamicStatus)
                : $DynamicStatus;
                break;

            case 'undelivered':
                $this->statusDescription = $DynamicStatus . "<br />" . GlobalHelper::convertDate($this->order->shipping->returnedAt);
                break;

            case 'delivered':
                $this->statusDescription = $DynamicStatus . "<br />" . GlobalHelper::convertDate($this->order->shipping->deliveredAt);
                break;

            case 'waiting-pick-up':
                $this->statusDescription = $DynamicStatus . "<br>" . GlobalHelper::convertDate($this->order->createdAt);
                break;

            default:
                if (in_array($this->order->status, $this->callcenterStatus)) {
                    // Convert the status update date for display
                    $statusUpdatedAt = GlobalHelper::convertDate($this->order->statusUpdatedAt);

                    // Determine the status description format based on order status
                    $this->statusDescription = $DynamicStatus." ".($this->order->status === 'reported'
                        ? " to $statusUpdatedAt"
                        : " <small>$statusUpdatedAt</small>");

                    // If the order has 'no-answer' status and is expired, mark it as expired
                    if ($this->order->status === 'no-answer' && $this->order->isexpired === 'yes') {
                        $this->statusDescription = "Expired";
                    }

                    // Append additional status information if the reason is set and is not 'followup'
                    if (!empty($this->order->statusReason) && strtolower($this->order->statusReason) !== 'followup') {
                        $this->statusDescription .= "<br><small>" . $this->ListeAdditionalInfo($this->order->statusReason) . "</small>";
                    }
                }

                break;
        }


        // Apply the transformation for the given status
        if (isset($status_map[$DynamicStatus])) {
            $this->statusDescription = $status_map[$DynamicStatus]();
        }
    }


   /**
     * Fetch additional information based on the provided key or return the full list.
     */
    public function ListeAdditionalInfo($key = null) {
        // Define the additional info statuses mapping
        $listeStatuts = [
            'cancelled-from-origin' => 'Order cancelled from origin',
            'duplicate-orders' => 'Duplicate orders',
            'didnt-order' => "Didn't order",
            'shipment-delay' => 'Shipment delay',
            'change-item' => 'Change item',
            'change-mind' => 'Change mind',
            'no-money' => 'No money',
            'high-shipping-charge' => 'High shipping charge',
            'cod-service-fee' => 'COD service fee',
            'high-taxes' => 'High taxes',
            'leave-shipping-address' => 'Leave shipping address',
            'bad-quality' => 'Bad quality',
            'coupon-balance-not-used' => 'Coupon/balance not used',
            'item-stocks-out' => 'Item stocks-out',
        ];

        // Return the specific status if a key is provided, or return the full list
        if ($key) {
            // Avoid errors using isset or null coalescing
            return $listeStatuts[$key] ?? $key;
        }

        return $listeStatuts;
    }

    /**
     * Fetch call center statuses.
     */
    public function getCallCenterStatuses(array $options = []): array {
        // Ensure 'keys' exists in the options and handle default case if not
        $arKeys = $options['arKeys'] ?? false;

        // Define the statuses mapping
        $statusList = [
            'waiting-pick-up' => "New lead",
            'picked-up' => "Confirmed",
            'no-answer' => "No answer",
            'canceled' => 'Canceled',
            'reported' => 'Schedule',
            'duplicate-order' => "Double orders",
            'pending' => "Pending",
            'failed' => "Wrong phone number",
            'test' => "Test",
        ];

        // Return either the keys or the full status list
        return $arKeys ? array_keys($statusList) : $statusList;
    }

    /**
     * Fetch shipping statuses.
     */
    public function getShippingStatuses(array $options = []): array {
        // Ensure 'keys' exists in the options and handle default case if not
        $arKeys = $options['arKeys'] ?? false;

        // Define the shipping statuses mapping
        $statusList = [
            'in-transit' => 'Processing',
            'shipped' => 'In transit',
            'delivered' => 'Delivered',
            'undelivered' => 'Return',
        ];

        // Return either the keys or the full status list
        return $arKeys ? array_keys($statusList) : $statusList;
    }

    /**
     * Fetch followup statuses.
     */
    public function getFollowupStatuses(array $options = []): array {
        // Ensure 'keys' exists in the options and handle default case if not
        $arKeys = $options['arKeys'] ?? false;

        // Define the shipping statuses mapping
        $statusList = [
            'delivery' => 'Delivery again',
            'abandon' => 'Abandon',
            'no-answer' => 'No Answer',
            'wrong-number' => 'Wrong number',
            'callback' => 'Call back',
            'rtc' => 'Return to Client',
            'already-received' => 'Already received',
        ];

        // Return either the keys or the full status list
        return $arKeys ? array_keys($statusList) : $statusList;
    }

    /**
     * Fetch Payments Methods.
     */
    public function mapPaymentsMethods(array $options = []): array {
        // Ensure 'keys' exists in the options and handle default case if not
        $arKeys = $options['arKeys'] ?? false;

        // Define the shipping statuses mapping
        $statusList = [
            'cod' => 'COD',
            'prepaid' => 'Prepaid',
        ];

        // Return either the keys or the full status list
        return $arKeys ? array_keys($statusList) : $statusList;
    }

    /**
     * map List Statuses
     */
    public function mapListStatuses(array $options = []): array|string|null {
        $listStatus = $options['listStatus'] ?? null;

        if (!$listStatus) {
            return null;
        }

        return array_map(fn($status) => $this->mapStandardStatuses(['status' => $status]), $listStatus);
    }

    /**
     * Map Standard status
     */
    public function mapStandardStatuses(array $options = []): array|string|null {
        $statusList = [
            'waiting-pick-up' => [
                "name" => "newlead",
                "label" => "New Lead",
            ],
            'picked-up' => [
                "name" => "confirmed",
                "label" => "Confirmed",
            ],
            'no-answer' => [
                "name" => "noanswer",
                "label" => "No Answer",
            ],
            'canceled' => [
                "name" => "canceled",
                "label" => "Canceled",
            ],
            'reported' => [
                "name" => "schedule",
                "label" => "Scheduled",
            ],
            'duplicate-order' => [
                "name" => "doubleorder",
                "label" => "Duplicate Order",
            ],
            'pending' => [
                "name" => "pending",
                "label" => "Pending",
            ],
            'failed' => [
                "name" => "wrongphonenumber",
                "label" => "Wrong Phone Number",
            ],
            'test' => [
                "name" => "test",
                "label" => "Test",
            ],
            'in-transit' => [
                "name" => "processing",
                "label" => "Processing",
            ],
            'shipped' => [
                "name" => "intransit",
                "label" => "In Transit",
            ],
            'delivered' => [
                "name" => "delivered",
                "label" => "Delivered",
            ],
            'undelivered' => [
                "name" => "return",
                "label" => "Return",
            ],
        ];

        // If 'originStatus' is provided, return its mapped value or null if not found.
        if ($options['originStatus'] ?? null) {
            return $statusList[$options['originStatus']]['name'] ?? null;
        }

         // Retunrn label by order Status
        if ($options['orderStatus'] ?? null) {
            return $statusList[$options['orderStatus']]['label'] ?? null;
        }

        // If 'originStatus' is provided, return its mapped value or null if not found.
        if (!empty($options['status'])) {
            // Reverse mapping: "name" => "original_key"
            $reverseMap = array_column(array_map(fn($key, $value) => ['name' => $value['name'], 'key' => $key], array_keys($statusList), $statusList), 'key', 'name');

            return $reverseMap[$options['status']] ?? null;
        }

        // If 'keys' is true, return an array of all possible original statuses.
        if (!empty($options['arKeys'])) {
            return array_keys($statusList);
        }

        // If 'getList' is true, return the full mapping of statuses.
        if (!empty($options['getList'])) {
            return array_column($statusList, 'label', 'name');
        }

        // Default return an empty array if no valid option is provided.
        return [];
    }

    /**
     * Map Dynamic status
     */
    public function mapDynamicStatuses(array $options = []): array|string|null {
        $statusList = array_merge($this->getCallCenterStatuses(), $this->getShippingStatuses());

        // If 'originStatus' is provided, return its mapped value or null if not found.
        if (!empty($options['originStatus'])) {
            return $statusList[$options['originStatus']] ?? null;
        }

        // If 'keys' is true, return an array of all possible original statuses.
        if (!empty($options['arKeys'])) {
            return array_keys($statusList);
        }

        // If 'getList' is true, return the full mapping of statuses.
        if (!empty($options['getList'])) {
            return $statusList;
        }

        // Default return an empty array if no valid option is provided.
        return [];
    }

    /*
        Get Liste Followup Status
    */
    public function followupStatusList($options = [])
    {
        $formatter = $options['formatter'] ?? null;
        $active_status = $options['active_status'] ?? null;
        $originStatus = $options['originStatus'] ?? null;
        $orderStatus = $options['orderStatus'] ?? null;

        $followup_status_list = [
            'new' => ['label' => 'New', 'class' => 'btn-primary', 'active' => 1],
            'delivery' => ['label' => 'Delivery again', 'class' => 'btn-success', 'active' => 1, 'icon' => 'diamond'],
            'abandon' => ['label' => 'Abandon', 'class' => 'btn-dark', 'active' => 1, 'icon' => 'minus'],
            'no-answer' => ['label' => 'No Answer', 'class' => 'btn-warning', 'active' => 1, 'icon' => 'ban'],
            're-delivery' => ['label' => 'Re-delivery', 'class' => 'btn-info', 'active' => 0, 'icon' => 'plane'],
            'wrong-number' => ['label' => 'Wrong number', 'class' => 'btn-secondary', 'active' => 1, 'icon' => 'question'],
            'callback' => ['label' => 'Call back', 'class' => 'btn-warning', 'active' => 1, 'icon' => 'phone'],
            'rtc' => ['label' => 'Return to Client', 'class' => 'btn-danger', 'active' => 1, 'icon' => 'refresh'],
            'already-received' => ['label' => 'Already received', 'class' => 'btn-success', 'active' => 1, 'icon' => 'check'],
            'cnee-pick-up' => ['label' => 'Cnee pick up', 'class' => 'btn-success', 'active' => 0],
            'cnee-feedback-received' => ['label' => 'Cnee feedback received', 'class' => 'btn-success', 'active' => 0],
            'cnee-reject-order' => ['label' => 'Cnee reject order', 'class' => 'btn-success', 'active' => 0],
            'cnee-need-help' => ['label' => 'Cnee need help', 'class' => 'btn-success', 'active' => 0],
            'inbound' => ['label' => 'Unpacking', 'class' => 'btn-success', 'active' => 0],
            'Temporary' => ['label' => 'Temporary Storage', 'class' => 'btn-success', 'active' => 0],
        ];

        // Formatter List
        $formatterList = array_map(fn($item) => $item['label'], $followup_status_list);

        // Filter by active status if needed
        if ($active_status === 1) {
            return array_keys(array_filter($followup_status_list, fn($item) => $item['active'] === 1));
        }

        // If 'originStatus' is provided, return its mapped value or null if not found.
        if ($originStatus) {
            return $formatterList[$originStatus] ?? '';
        }

        // Return formatted results or labels
        if (!$formatter) {
            return $formatterList;
        }

        // Return all followup status data by default
        return $followup_status_list;
    }

    /**
     * Ensure that the order is an object.
     */
    private function ensureOrderIsObject($order) {
        return is_array($order) ? json_decode(json_encode($order)) : $order;
    }

}