<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\User;
use App\Services\Sellers\FilterSellers;
use App\Services\TwoFactorAuth\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use SellersHelper;

class TwoFactorAuthController extends Controller
{
    protected $twoFactorAuthService;
    protected $currentSeller;
    protected FilterSellers $FilterSellers;

    public function __construct(TwoFactorAuthService $twoFactorAuthService)
    {
        $this->twoFactorAuthService = $twoFactorAuthService;
        $this->currentSeller = SellersHelper::CurrentSeller();
        $this->FilterSellers = new FilterSellers();
    }

    /**
     * Toggle 2FA
     *
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function Toggle2FA(Request $request){

        $validateToggle2FA = $this->validateToggle2FA($request);
        if ($validateToggle2FA['response'] === 'error') {
            return response()->json($validateToggle2FA, 422);
        }

        if (!Hash::check($request->password, $this->currentSeller->user->password)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid password',
            ], 422);
        }

        $response = $this->handleTwoFactoreService($request->enable , $this->currentSeller);

        return response()->json($response, 200);
    }



    /**
     * Verify and enable 2FA
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        $validateOTP = $this->validateOTP($request);
        if ($validateOTP['response'] === 'error') {
            return response()->json($validateOTP, 422);
        }

        $verifyCode = $this->twoFactorAuthService->verifyCode($validateOTP['code'], $validateOTP['seller'] );
        if ($verifyCode['response'] === 'error') {
            return response()->json($verifyCode, 422);
        }

          // Login Seller
          $dataProcessLogin = $this->processLoginSeller($validateOTP['seller']);

          return response()->json([
              'response' => 'success',
              'message' => 'Login successful',
              'result' => [
                  'accessToken' => $dataProcessLogin['token'],
                  'user' => $this->renderUserInfos($dataProcessLogin['user']),
              ],
          ], 200);




    }


    /**
     * Get the current 2FA status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function status()
    {
        return response()->json([
            'response' => 'success',
            'result' =>  $this->twoFactorAuthService->is2FAEnabled($this->currentSeller) ?
            [
                'enabled' => true,
                'secret' => $this->currentSeller->two_factor_secret,
                'qrCode' => $this->twoFactorAuthService->getQRCodeUrl($this->currentSeller, $this->currentSeller->two_factor_secret),
            ] : [
                'enabled' => false,
                'secret' => null,
                'qrCode' => null,
            ],

        ]);
    }

    /**
     * Handle 2FA service
     */
    private function handleTwoFactoreService($enable, $seller){
        if ($enable) {

            // Generate 2FA secret and QR code
            $secretKey = $this->twoFactorAuthService->generateSecretKey($seller);
            $qrCodeUrl = $this->twoFactorAuthService->getQRCodeUrl($seller, $secretKey);

            return [
                'response' => 'success',
                'message' => '2FA has been enabled',
                'result' => [
                    'enabled' => true,
                    'secret' => $secretKey,
                    'qrCode' => $qrCodeUrl
                ]
            ];
        } else {
            // Disable 2FA
            $this->twoFactorAuthService->disable2FA($seller);

            return [
                'response' => 'success',
                'message' => '2FA has been disabled',
                'result' => [
                    'enabled' => false,
                    'secret' => null,
                    'qrCode' => null
                ]
            ];
        }
    }

    /**
     * Validate Toggle 2FA
     */
    private function validateToggle2FA(Request $request){
        $validator = Validator::make($request->all(), [
            'enable' => 'required|boolean',
            'password' => 'required|string',
        ]);
        if ($validator->fails()) {
            return[
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ];
        }

        return ['response' => 'success'
                    ,'enable' => $request->enable,
                    'password' => $request->password,
                ];
    }

    /**
     * render User Infos
     */
    private function renderUserInfos($user){
        // Retrieve the seller record
        return $this->FilterSellers->getSellers([
            "user_id" => $user->id,
            "first" => 1,
        ]);
    }


/**
     * process Login Seller
     */
    private function processLoginSeller($seller,$user = []){
        // Get associated user
        $user = $user ? $user : User::findOrFail($seller->user_id);

        // Generate Passport token
        $token = $user->createToken('token')->accessToken;

        // Return Success Response
        return [
            'user' => $user,
            'token' => $token,
        ];
    }
    private function validateOTP(Request $request){
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
            'sellerId' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return[
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ];
        }
        $seller = Sellers::find($request->sellerId);
        if (!$seller) {
            return[
                'response' => 'error',
                'message' => 'Seller not found',
            ];
        }

        return ['response' => 'success'
                    ,'code' => $request->code,
                    'seller' => $seller,
                ];
    }

}