<?php
class GlobalHelper{
    /**
     * Convert a given list to an array and filter out empty values.
     */
    public static function ConvertToArray(array $options = []): array {
        $liste = $options['liste'] ?? [];

        // Ensure $liste is an array
        $liste = json_decode(json_encode($liste), true);
        $liste = array_filter($liste ?? []);

        // Return new list
        return array_filter($liste);
    }

    /**
     * Generate a random token
     *
     * @return string Generated token
     */
    public static function generateToken(): string {
        return bin2hex(random_bytes(16));
    }
    /**
     * Format a number with space as thousands separator and comma as decimal separator
     *
     * @param float|int|string $number The number to format
     * @param int $decimals Number of decimal places (default: 2)
     * @return string Formatted number
     */
    public static function formatNumber($number, $decimals = 2)
    {
        // Convert to float in case it's a string
        $number = (float) $number;

        // Format the number with specified decimal places
        $formatted = number_format($number, $decimals, ',', ' ');

        return $formatted;
    }

    /**
     * Check if the value is a valid date.
     */
    public static function CheckIsDate(array $options = []): bool {
        $value = $options['value'] ?? null;
        $key = $options['key'] ?? null;
        $format = $options['format'] ?? 'Y-m-d';

        if ($value) {
            if (strpos($key, 'date') !== false) {
                return true;
            } else {
                if (!is_string($value)) {
                    return false; // Ou gérer autrement selon le contexte
                }

                $dateTime = DateTime::createFromFormat($format, $value);
                return $dateTime && $dateTime->format($format) === $value;
            }
        }

        return false;
    }

    /**
     * Clean the date by checking for invalid or default date values.
     */
    public static function CleanDate(?string $date): ?string {
        $invalidDates = ['0000-00-00', '01-01-1970', '01/01/1970'];

        if (in_array($date, $invalidDates, true)) {
            return null;
        }

        return $date;
    }
    /**
     * Convert Date
     */
    public static function convertDate($date, $format = "NULL") {
        // Initialize result variable
        $result = "";

        // Ensure the input date is valid and doesn't contain "0000"
        if ($date && !str_contains($date, "0000")) {
            $timestamp = strtotime($date);

            // If the format is NULL and the time part is not midnight (00:00), use a date-time format
            if ($format === "NULL") {
                $format = (date("H:i", $timestamp) !== "00:00") ? "d.m.Y H:i" : "d.m.Y";
            }

            // Format the date using the provided or default format
            $result = date($format, $timestamp);
        }

        // Return the result or an empty string if the date was invalid
        return $result;
    }

    /**
     * Insert a value or key/value
     */
    public static function arrayInsertAfter( array $array, $key, $new ) {
        $keys = array_keys( $array );
        $index = array_search( $key, $keys );
        $pos = false === $index ? count( $array ) : $index + 1;

        return array_merge( array_slice( $array, 0, $pos ), $new, array_slice( $array, $pos ) );
    }

    /**
	 * Remove all emojis from a string
	 *
	 * @param string $string Input string
	 * @return string String without emojis
	 */
	public static function RemoveEmoji($string, $replacement = '') {
        // Validate the input
        if ($string) {
            // Match emojis using Unicode properties and expanded ranges
            $regex_emoji = '/[\p{So}\p{Sk}\p{Sc}\p{Sm}\p{Pc}' .
                '\x{1F600}-\x{1F64F}' .  // Emoticons
                '\x{1F300}-\x{1F5FF}' .  // Miscellaneous Symbols and Pictographs
                '\x{1F680}-\x{1F6FF}' .  // Transport and Map Symbols
                '\x{1F1E6}-\x{1F1FF}' .  // Flags
                '\x{2600}-\x{26FF}' .    // Miscellaneous Symbols
                '\x{2700}-\x{27BF}' .    // Dingbats
                '\x{1F900}-\x{1F9FF}' .  // Supplemental Symbols and Pictographs
                '\x{1FA70}-\x{1FAFF}' .  // Symbols and Pictographs Extended-A
                '\x{1F000}-\x{1F02F}' .  // Mahjong Tiles
                '\x{1F0A0}-\x{1F0FF}' .  // Playing Cards
                '\x{1F700}-\x{1F77F}' .  // Alchemical Symbols
                ']/u';

            // Remove emojis
            $string = preg_replace($regex_emoji, $replacement, $string);

            // Normalize styled Unicode (italic, bold, etc.) to plain text
            $string = preg_replace_callback(
                '/[\x{1D400}-\x{1D7FF}]/u', // Match styled Unicode (e.g., bold, italic letters)
                function ($matches) {
                    $charCode = mb_ord($matches[0], 'UTF-8');
                    // Map to corresponding plain text character
                    if ($charCode >= 0x1D400 && $charCode <= 0x1D7FF) {
                        return chr(($charCode - 0x1D400) % 26 + ord('a')); // A-Z range
                    }
                    return $matches[0];
                },
                $string
            );
        }

        return $string;
    }

    /**
     * execQuery
     */
    public static function execQuery($query){
        // Get raw SQL and bindings
        $sql = $query->toSql();
        $bindings = $query->getBindings();

        // Replace placeholders (`?`) with the actual bindings
        $fullQuery = vsprintf(
            str_replace('?', '%s', $sql),
            array_map(function ($binding) {
                // Ensure strings are properly quoted
                return is_numeric($binding) ? $binding : "'".addslashes($binding)."'";
            }, $bindings)
        );

        // Return Result
        return $fullQuery;
    }


    /**
     * Calcul Rate
     */
    public static function calculRate($options = []){
        $total = isset($options['total']) ? (float) $options['total'] : 0;
        $baseTotal = isset($options['baseTotal']) ? (float) $options['baseTotal'] : 0;

        // Initial Result
        $rate = 0;

        // Calcul Rate
        if($baseTotal > 0){
            $rate = ($total * 100) / $baseTotal;
            $rate = round($rate, 2);
        }

        // Return result
        return $rate;
    }


    /**
     * Rounds a number up to the nearest multiple of a given significance.
     */
    public static function convertCeiling($number, $significance = 1){
        // Validate inputs: both must be numeric
        if (!is_numeric($number) || !is_numeric($significance)) {
            return false;
        }

        // Round up the number to the nearest multiple of the significance
        return ceil($number / $significance) * $significance;
    }
}