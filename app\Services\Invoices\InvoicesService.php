<?php
namespace App\Services\Invoices;

class InvoicesService {

    public function __construct() {
    }

    /**
     * Get Liste Statuts
     */
    public function InvoiceStatus($key = null) {
        $liste_statuts = [
            'awaiting' => "Unpaid",
            'paid' => "Paid",
        ];
    
        if ($key === "list") {
            return $liste_statuts;
        }
    
        return $liste_statuts[$key] ?? "";
    }
}