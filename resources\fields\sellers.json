{"table": "sellers", "fields": {"id": {"dbName": "id", "layout": "general"}, "userID": {"dbName": "user_id", "layout": "details"}, "fullname": {"dbName": "fullname", "layout": "general", "updateValidation": "required|string|max:255"}, "email": {"dbName": "email", "layout": "general", "updateValidation": "required|email|unique:sellers,email,{id},id"}, "cardID": {"dbName": "cin", "layout": "general", "updateValidation": "nullable|string|max:255"}, "phoneNumber": {"dbName": "mobile", "layout": "general", "updateValidation": "required|string|max:255"}, "address": {"dbName": "address", "layout": "general", "updateValidation": "required|string|max:255"}, "legalName": {"dbName": "name_pro", "layout": "general", "updateValidation": "nullable|string|max:255"}, "commercialRegister": {"dbName": "register_number", "layout": "general", "updateValidation": "nullable|string|max:255"}, "taxID": {"dbName": "tax_identification", "layout": "general", "updateValidation": "nullable|string|max:255"}, "confirmationService": {"dbName": "manage_callcenter", "layout": "general"}, "shippingService": {"dbName": "manage_shipping", "layout": "general"}, "logo": {"dbRAW": "'' AS logo", "layout": "general"}, "permissions": {"dbName": "permissions", "layout": "general"}}}