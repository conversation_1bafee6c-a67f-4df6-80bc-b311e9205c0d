<?php
namespace App\Services\Fields;

use App\Services\Fields\FieldsService;
use GlobalHelper;
use SellersHelper;

class FormatterSourcing {
    protected $FieldsService;
    protected $currentSeller;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Format the sourcing requests based on provided options
     */
    public function formatterResult(array $options = []): array|null {
        // Extract options
        $first = $options['first'] ?? null;
        $items = $options['items'] ?? [];

        // Convert to array using GlobalHelper
        $items = GlobalHelper::ConvertToArray(['liste' => $items]);

        // Return empty array if no items are provided
        if (empty($items)) {
            return $first === 1 ? null : ['items' => []];
        }

        // Format the items
        $formattedItems = array_map(function ($row) {
            $groupedData = $this->FieldsService->GroupeFields('sourcingRequest', $row);
            return $this->FormatterSourcingRequest($groupedData);
        }, $items);

        // Return either the first item or all formatted items
        return $first === 1 ? ($formattedItems[0] ?? null) : ['items' => $formattedItems];
    }

    /**
     * Formatter for an individual sourcing request
     */
    private function FormatterSourcingRequest(array $row_sourcing): array {
        // Remove unwanted fields
        unset($row_sourcing['seller_id']);
        unset($row_sourcing['seller_name']);
        unset($row_sourcing['created_by']);
        unset($row_sourcing['updated_by']);

        // Convert snake_case to camelCase for all fields
        $formatted = [];
        foreach ($row_sourcing as $key => $value) {
            $camelKey = lcfirst(str_replace(' ', '', ucwords(str_replace('_', ' ', $key))));
            $formatted[$camelKey] = $value;
        }

        // Add additional fields that are not in sourcingRequest.json but needed
        if (isset($row_sourcing['number_of_products'])) {
            $formatted['numberOfProducts'] = $row_sourcing['number_of_products'];
        }
        if (isset($row_sourcing['total_quantity'])) {
            $formatted['totalQuantity'] = $row_sourcing['total_quantity'];
        }
        if (isset($row_sourcing['products'])) {
            $formatted['products'] = $row_sourcing['products'];
        }

        return $formatted;
    }

    /**
     * Formatter general infos sourcing request
     */
    private function FormatterGeneral(array $row_sourcing): array {
        // Add any general formatting here
        return $row_sourcing;
    }

    /**
     * Formatter for the product details within a sourcing request
     */
    private function FormatterProducts(array $row_sourcing): array {
        // Format products string if it exists
        if (isset($row_sourcing['products'])) {
            $row_sourcing['products'] = $this->formatProductsString($row_sourcing['products']);
        }

        return $row_sourcing;
    }

    /**
     * Formatter for the status details within a sourcing request
     */
    private function FormatterStatus(array $row_sourcing): array {
        // Map status to a more user-friendly format if needed
        if (isset($row_sourcing['status'])) {
            $row_sourcing['status'] = $this->mapStatus($row_sourcing['status']);
        }

        return $row_sourcing;
    }

    /**
     * Format products string
     */
    private function formatProductsString($products): string {
        if (is_array($products)) {
            return implode('<br>', array_map(function($product) {
                return "{$product['quantity']} * {$product['name']}";
            }, $products));
        }
        return $products;
    }

    /**
     * Map status to user-friendly format
     */
    private function mapStatus($status): string {
        $statusMap = [
            'created' => 'Created',
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'deleted' => 'Deleted'
        ];

        return $statusMap[$status] ?? $status;
    }
}