<?php

namespace App\Http\Controllers\Orders;

use App\Http\Controllers\Controller;
use App\Services\Orders\OrderStatusService;
use App\Services\Orders\FilterOrders;
use App\Services\Orders\OrderService;
use App\Services\Validator\OrdersValidator;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use SellersHelper;

class OrderExportController extends Controller{
    protected FilterOrders $filterOrders;
    protected OrderStatusService $OrderStatusService;
    protected OrdersValidator $OrdersValidator;
    protected OrderService $OrderService;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->filterOrders = new FilterOrders();
        $this->OrderStatusService = new OrderStatusService();
        $this->OrdersValidator = new OrdersValidator();
        $this->OrderService = new OrderService();
    }

    /**
     * Export orders
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function export(Request $request): JsonResponse{
        // Validate if both parameters are present
        $validation = $this->validateExportOrders($request);
        if ($validation['response'] == "error") { return response()->json($validation, 400); }

        // Retrieve orders with the provided filters
        $filters = $request->all();
        $filters['noPaginate'] = 1;
        $filters['layout'] = "general,details";
        $result = $this->filterOrders->getOrders($filters);

        // Generate Excel Data
        $excelData = $request->followup ? $this->folowupExcelData($result) : $this->ordersExcelData($result);

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $excelData,
        ]);
    }

    /**
     * Generate data for Excel export
     *
     * @param array $orders
     * @return array
     */
    private function ordersExcelData($orders): array{
        $excelData = [];

        // Header Row
        $excelHeader = [
            trans('orders.Order Number'),
            trans('orders.Mobile number'),
            trans('orders.Whatsapp number'),
            trans('orders.Seconde phone number'),
            trans('orders.Called date'),
            trans('orders.Pay Date'),
            trans('orders.Payment Method'),
            trans('orders.Platform'),
            trans('orders.Total Amount'),
            trans('orders.Currency'),
            trans('orders.Consignee'),
            trans('orders.Call Result'),
            trans('orders.Is It A Problem Order'),
            trans('orders.Reason'),
            trans('orders.Remark'),
            trans('orders.Country'),
            trans('orders.Province'),
            trans('orders.City'),
            trans('orders.District/area name'),
            trans('orders.Address (street, house & landmark)'),
            trans('orders.Language'),
            trans('orders.Note for shipping'),
            trans('orders.Channel'),
            trans('orders.GPS coordinates'),
            trans('orders.Products'),
            trans('orders.SKU'),
            trans('orders.Qty'),
        ];

        // Check is chickpoint
        if (!SellersHelper::IsChickPoint()) {
            unset($excelHeader[12]);
        }

        // Add header to excel data
        $excelData[] = $excelHeader;

        // add orders to excel data
        if ($orders) {
            foreach ($orders as $order) {
                // Problem Info
                if ($order->status !== 'newlead' && $order->isConfirmed !== 'yes') {
                    $orderProblem = 'Problem Order';
                    if (strtolower($order->moreInfo) !== 'followup') {
                        $problemDescription = !empty($order->moreInfo) ? $order->moreInfo : $order->unconfirmedReason;
                    }
                }

                // Consignee Infos
                $consignee = $order->consignee;

                // Product Details
                $productDetails = $this->OrderService->extractProductsDetails($order);

                // Row Data
                $rowData = [
                    $order->orderNum ?: $order->orderCode,
                    $consignee->mobileNumber ?? null,
                    $consignee->whatsappNumber ?? null,
                    $consignee->phoneNumber ?? null,
                    GlobalHelper::convertDate($order->statusUpdatedAt ?? null),
                    GlobalHelper::convertDate($order->createdAt ?? null),
                    strtoupper($order->paymentMethod ?? ''),
                    $order->store ?? null,
                    $order->goodsValue ?? null,
                    $order->currency ?? null,
                    $consignee->contact ?? null,
                    strip_tags(str_replace('<br />', ' ', $order->statusDescription ?? '')),
                    $orderProblem ?? null,
                    $problemDescription ?? null,
                    $order->unconfirmedDescription ?? null,
                    $consignee->country ?? null,
                    $consignee->province ?? null,
                    $consignee->city ?? null,
                    $consignee->area ?? null,
                    $consignee->address ?? null,
                    $consignee->language ?: "ar",
                    $order->description ?? null,
                    in_array($order->validatedVia, ["whatsapp", "chatbot"]) ? "whatsApp" : "phone",
                    ($consignee->latitude ?? '') . "," . ($consignee->longitude ?? ''),
                    $productDetails['products'],
                    $productDetails['sku'],
                    $productDetails['qty'],
                ];

                // Check is chickpoint
                if (!SellersHelper::IsChickPoint()) {
                    unset($rowData[12]);
                }

                // Generate Excel Data
                $excelData[] = $rowData;
            }
        }

        return $excelData;
    }

    /**
     * Generate data for Excel export Followup
     *
     * @param array $orders
     * @return array
     */
    private function folowupExcelData($orders): array{
        $excelData = [];

        // Header Row
        $excelData[] = [
            trans('orders.Order Number'),
            trans('orders.Tracking Number'),
            trans('orders.Consignee'),
            trans('orders.Mobile number'),
            trans('orders.Whatsapp number'),
            trans('orders.Seconde phone number'),
            trans('orders.Country'),
            trans('orders.Province'),
            trans('orders.City'),
            trans('orders.District'),
            trans('orders.Address'),
            trans('orders.Shipped Amount'),
            trans('orders.Currency'),
            trans('orders.Call Result'),
            trans('orders.Refuse Reason'),
            trans('orders.Appointment delivery time'),
            trans('orders.Note'),
            trans('orders.Call Date'),
            trans('orders.Note for shipping'),
            trans('orders.Created'),
        ];

        if ($orders) {
            foreach ($orders as $order) {
                // Consignee Infos
                $consignee = $order->consignee;

                // Generate Excel Data
                $excelData[] = [
                    $order->orderNum ?: $order->orderCode,
                    $order->trackingNumber,
                    $consignee->contact ?? null,
                    $consignee->mobileNumber ?? null,
                    $consignee->whatsappNumber ?? null,
                    $consignee->phoneNumber ?? null,
                    $consignee->country ?? null,
                    $consignee->province ?? null,
                    $consignee->city ?? null,
                    $consignee->area ?? null,
                    $consignee->address ?? null,
                    $order->goodsValue ?? null,
                    $order->currency ?? null,
                    $order->followup->status ?? null,
                    $order->followup->rejectReason ?? null,
                    GlobalHelper::convertDate($order->followup->scheduleDate ?? null),
                    $order->followup->comment ?? null,
                    GlobalHelper::convertDate($order->followup->statusUpdatedAt ?? null),
                    $order->description ?? null,
                    GlobalHelper::convertDate($order->followup->createdAt ?? null),
                ];
            }
        }

        return $excelData;
    }

    /**
     * Validate Export orders
     */
    private function validateExportOrders(Request $request): array {
        $startDate = $request->input('startDate');
        $endDate = $request->input('endDate');
        $keyword = $request->input('keyword');
        $ids = $request->input('ids');

        if ($keyword || ($startDate && $endDate) || (is_array($ids) && count($ids) > 0)) {
            return ['response' => 'success'];
        }

        return [
            'response' => 'error',
            'message' => 'Please provide either both start_date and end_date, a keyword, or a list of IDs.',
        ];
    }
}