<?php
namespace App\Services\Fields;
use App\Services\Fields\FieldsService;
use App\Services\Invoices\InvoicesService;
use GlobalHelper;
use SellersHelper;

class FormatterInvoices {

    protected $FieldsService;
    protected $currentSeller;
    protected $InvoicesService;
    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->InvoicesService = new InvoicesService();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }
    
    /**
     * Format the orders based on provided options
     */
    public function formatterResult(array $options = []): array|null{
        // Extract options
        $first = $options['first'] ?? null;
        $items = $options['items'] ?? [];

        // Convert to array using GlobalHelper
        $items = GlobalHelper::ConvertToArray(['liste' => $items]);

        // Return empty array if no items are provided
        if (empty($items)) {
            return $first === 1 ? null : ['items' => []];
        }

        // Format the items
        $formattedItems = array_map(function ($row) {
            $groupedData = $this->FieldsService->GroupeFields('invoices', $row);
            return $this->FormatterInvoice($groupedData);
        }, $items);

        // Return either the first item or all formatted items
        return $first === 1 ? ($formattedItems[0] ?? null) : ['items' => $formattedItems];
    }

    /**
     * Formatter for an individual order
     */
    private function FormatterInvoice(array $row_invoice): array{
        // Format Media
        $row_invoice = $this->FormatterStatus($row_invoice);

        // return order
        return $row_invoice;
    }

    /**
     * Formatter for the product details within an order
     */
    private function FormatterStatus(array $row_invoice): array{
    
        // Check if both 'goodsDescription' and 'sku' exist in the array
        if (array_key_exists('status', $row_invoice)) {
            $statusDescription = $this->InvoicesService->InvoiceStatus($row_invoice['status']);
            $row_invoice = GlobalHelper::arrayInsertAfter($row_invoice,"status",['statusDescription' => $statusDescription]);
        }
    
        return $row_invoice;
    }
}
