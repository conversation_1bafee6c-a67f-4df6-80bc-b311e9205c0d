<?php

use App\Models\Sellers;
use App\Services\Sellers\FilterSellers;

class SellersHelper{
    /**
     * Get Current Seller
     */
    public static function CurrentSeller(): Sellers {
        return app()->bound('currentSeller') ? app('currentSeller') : new Sellers();
    }

    /**
     * render User Infos
     */
    public static function renderUserInfos(){
        // Use auth('api') instead of $request->user()
        $user = auth('api')->user();

        // Retrieve the seller record
        $FilterSellers = new FilterSellers();
        return $FilterSellers->getSellers([
            "user_id" => $user->id,
            "first" => 1,
            "layout" => "general,details"
        ]);
    }

   /**
     * Check Seller Permission
     */
    public static function CheckPermission($row_seller, $permission){
        // Check if permission is provided
        if ($permission) {
            // Get permissions from the seller's data
            $seller_permissions = json_decode($row_seller->permissions, true);

            // Check if the permissions are valid and not empty
            if ($seller_permissions) {
                // Define a mapping of specific permission checks
                $permission_map = [
                    'sellers.dashbord.funds' => "Dashbord Funds",
                    'sellers.dashbord.followup' => "Dashbord Followup",
                    'sellers.trackingnumber' => "Tracking Number",
                    'sellers.shippingcompany' => "Shipping Company",
                    'sellers.shipping' => "Shipping History",
                    'sellers.confirmation' => "Confirmation History",
                    'sellers.followuporders' => "Followup Orders",
                    'sellers.followup' => "Followup History",
                    'sellers.calls' => "Confirmation Calls",
                    'sellers.realrates.confirmation' => "Confirmation Real Rates",
                    'sellers.realrates.shipping' => "Shipping Real Rates",
                    'sellers.deleteorders' => "Delete Orders",
                    'sellers.ordernote' => "Order Notes",
                ];

                // Check if the permission exists in the predefined map and if the seller has it
                if (isset($permission_map[$permission]) && in_array($permission_map[$permission], $seller_permissions)) {
                    return true;
                }

                // Check if the permission is directly listed in the seller's permissions
                if (in_array($permission, $seller_permissions)) {
                    return true;
                }
            }
        }

        // Default return if no conditions are met
        return false;
    }


	/*
        IsChickPoint
    */
	public static function IsChickPoint($sellerId = ""){
		$sellerId = $sellerId ?: Self::CurrentSeller()->id;
        if (in_array($sellerId,[205, 243])) {
			return true;
		}

		return false;
	}

    

}
 