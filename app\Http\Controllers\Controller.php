<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

abstract class Controller
{
    /**
     * success response method for all codp api project
     *
     * @param $result
     * @param int $status
     * @return JsonResponse
     */

    public function sendResponse($result, $status = 200)

    {

        $response = [
            'reponse' => "success",
            'result' => $result,
        ];


        return response()->json($response, $status);
    }


    /**
     * return error response.
     *
     * @param array $errorMessages
     * @param int $statut
     * @return JsonResponse
     */

    public function sendError($errorMessages = [], $statut = 400)
    {
        $response = [

            'reponse' => "error",
            'message' => $errorMessages,
            "statut" => $statut

        ];

        return response()->json($response, $statut);
    }

    /**
     * Convert array keys to camelCase.
     */
    public function toCamelCase($array)
    {
        $result = [];
        foreach ($array as $key => $value) {
            $camelKey = Str::camel($key); // Use Str::camel() instead of camel_case()
            $result[$camelKey] = is_array($value) ? $this->toCamelCase($value) : $value;
        }
        return $result;
    }
}
