<?php

namespace App\Http\Controllers\Warehousing;

use App\Http\Controllers\Controller;
use App\Models\SellersStock;
use App\Models\SellersStockvariants;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\SellersStockvariantsPrices;
use App\Services\Fields\FieldsService;
use App\Services\Validator\ProductsValidator;
use App\Services\Warehousing\ProductsService;
use SellersHelper;

class ProductsVariantController extends Controller{
    protected ProductsValidator $productsValidator;
    protected FieldsService $FieldsService;
    protected ProductsController $productsController;
    protected $currentSeller;
    protected ProductsService $productsService;

    public function __construct(){
        $this->productsValidator = new ProductsValidator();
        $this->FieldsService = new FieldsService();
        $this->productsController = new ProductsController();
        $this->productsService = new ProductsService();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }


    /**
     * Create a new product variant.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request, $productId): JsonResponse
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct([
            'id' => $productId,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]], [], true);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'productVariants',
            'option' => 'createValidation',
            'data' => $request->all(),

        ]);

        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'store',
            'type' => 'productVariants',
            'data' => $request->all(),
            'AppendParams' => [
                'seller_id' =>  $this->currentSeller->id,
                'seller_name' =>  $this->currentSeller->fullname,
                'parent' => $productId,
            ]

        ]);

        // Select only 'id', 'name', 'sku' after creation
        $variant = SellersStock::create($processData);
         //get product details
         $productDetails = $this->productDetails($request,$productId);

         // Return the response with the new offer and its prices
         return response()->json([
             'response' => 'success',
             'message' => __('products.offer_created_successfully'),
             'result' => $productDetails,
         ], 201);
    }




    /**
     * update a product variant.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request, $productId, $variantId)
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]],[], true);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate the variant
        $validationResult = $this->productsValidator->validateVariant($productId, $variantId);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 404);
        }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'productVariants',
            'option' => 'updateValidation',
            'data' => $request->all(),
            'replaceParams' => [
                "{id}" => $variantId
            ]
        ]);

        if ($resultValidator['response'] == "error") {
            return response()->json($resultValidator, 400);
        }

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'update',
            'type' => 'productVariants',
            'data' => $request->all(),
        ]);

        // Update the variant
        $variant = $validationResult['variant'];
        $variant->update($processData);

        //get product details
        $productDetails = $this->productDetails($request,$productId);

        // Return the response with the updated variant and its prices
        return response()->json([
            'response' => 'success',
            'message' => __('products.variant_updated_successfully'),
            'result' => $productDetails,
        ]);

    }


    /**
     * Delete a product variant.
     *
     * @param int $productId
     * @param int $variantId
     * @return JsonResponse
     */
    public function destroy($productId, $variantId)
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $productId, 'layout' => 'details', "with" => ["salesPrices.prices", "upsell.prices"]],[], true);
        if ($validateProduct['response'] == 'error') {
            return response()->json($validateProduct, 404);
        }

        // Validate the variant
        $validationResult = $this->productsValidator->validateVariant($productId, $variantId);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 404);
        }


        // Delete the variant
        $validationResult['variant']->delete();

        // Return the response
        return response()->json([
            'response' => 'success',
            'message' => __('products.variant_deleted_successfully'),
        ]);
    }


/**
     * Get product details from products controller
     */
    private function productDetails($request,$id){
        // Call the details method (for product)
        $detailsResponse = $this->productsController->details($request, $id);

        // Extract the data from the JsonResponse object
        return $detailsResponse->getData(true)['result'];
    }


/**
 * Create multiple product variants using combinations.
 *
 * @param Request $request
 * @param int $productId
 * @return JsonResponse
 */
public function storeWithCombination(Request $request, $productId): JsonResponse
{
    // Validate Product
    $validateProduct = $this->productsValidator->validateProduct([
        'id' => $productId,
        'layout' => 'details',
        "with" => ["salesPrices.prices", "upsell.prices"]
    ], [], true);

    if ($validateProduct['response'] == 'error') {
        return response()->json($validateProduct, 404);
    }

    // Validate combinations array exists
    if (!$request->has('combinations') || !is_array($request->combinations)) {
        return response()->json([
            'response' => 'error',
            'message' => 'Combinations array is required'
        ], 400);
    }

    try {
        // Get product base name
        $productName = $validateProduct['rowProduct']->name;

        $combinations = $this->productsService->Combinations($request->combinations);

        $createdVariants = [];
        $unsavedVariants = [];

        // Get current variant count for SKU indexing
        $currentVariantCount = SellersStock::where('parent', $productId)->count();

        foreach ($combinations as $index => $combination) {
            // Create variant name by combining product name with attributes
            $variantName = $productName . '-' . strtolower(implode('-', $combination));

            // Create unique SKU
            $variantSku = $productName . '-' . strtolower(implode('-', $combination));

            // Check if SKU already exists
            $existingVariant = SellersStock::where('reference', $variantSku)->first();
            if ($existingVariant) {
                // If SKU already exists, add the variant to the unsavedVariants list and continue
                $unsavedVariants[] = [
                    'name' => $variantName,
                    'sku' => $variantSku,
                ];
                continue;
            }

            // Prepare variant data
            $variantData = [
                'name' => $variantName,
                'sku' => $variantSku,
            ];

            // Process data for the variant
            $processData = $this->FieldsService->ProcessData([
                'option' => 'store',
                'type' => 'productVariants',
                'data' => $variantData,
                'AppendParams' => [
                    'seller_id' => $this->currentSeller->id,
                    'seller_name' => $this->currentSeller->fullname,
                    'parent' => $productId,
                ]
            ]);

            // Create the variant
            $variant = SellersStock::create($processData);
            $createdVariants[] = $variant;
        }

        // Get updated product details
        $productDetails = $this->productDetails($request, $productId);

        return response()->json([
            'response' => 'success',
            'message' => __('products.variants_created_successfully'),
            'result' => $productDetails,
            'unsavedVariants' => $unsavedVariants, // Return unsaved variants that were skipped
        ], 201);

    } catch (\Exception $e) {
        return response()->json([
            'response' => 'error',
            'message' => 'Failed to create variants: ' . $e->getMessage()
        ], 500);
    }


}

}