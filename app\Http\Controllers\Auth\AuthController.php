<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\SendCodeResetPassword;
use App\Models\ResetCodePassword;
use App\Models\Sellers;
use App\Models\User;
use App\Services\Fields\FieldsService;
use App\Services\Notifications\MailService;
use App\Services\Sellers\FilterSellers;
use App\Services\TwoFactorAuth\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller{
    protected FilterSellers $FilterSellers;
    protected TwoFactorAuthService $twoFactorAuthService;
    protected MailService $mailService;

    /**
     * Construct
     */
    public function __construct(TwoFactorAuthService $twoFactorAuthService){
        $this->FilterSellers = new FilterSellers();
        $this->twoFactorAuthService = $twoFactorAuthService;
        $this->mailService = new MailService();
    }

    /**
     * Login
     */
    public function login(Request $request){
        $validateLogin = $this->validateLogin($request);
        if ($validateLogin['response'] === 'error') { return response()->json($validateLogin, 400); }

        $validateUserPsw = $this->validateUserPsw($validateLogin['email'], $validateLogin['password']);
        if ($validateUserPsw['response'] === 'error') { return response()->json($validateUserPsw, 400); }


        // Check if 2FA is enabled for this user
        $check2fa = $this->Check2fa($validateUserPsw['user']->seller);
        if ($check2fa) { return response()->json($check2fa, 200); }

        // Login Seller
        $dataProcessLogin = $this->processLoginSeller($validateUserPsw['user']->seller);

        return response()->json([
            'response' => 'success',
            'message' => 'Login successful',
            'result' => [
                'accessToken' => $dataProcessLogin['token'],
                'user' => $this->renderUserInfos($dataProcessLogin['user']),
            ],
        ], 200);
    }

    /**
     * Auto Login By token
     */
    public function autoLogin($token){
        // Validate the request
        $validateAutoLogin = $this->validateAutoLogin($token);
        if ($validateAutoLogin['response'] === 'error') { return response()->json($validateAutoLogin, 400); }

        // Get Seller From Validate Data
        $seller = $validateAutoLogin['seller'];

        // Login Seller
        $dataProcessLogin = $this->processLoginSeller($seller);

        // Empty Token Seller After Auto Login
        $this->emptyTokenSeller($seller);

        // Return Succes Respo,se
        return response()->json([
            'response' => 'success',
            'message' => 'Auto login successful',
            'result' => [
                'accessToken' => $dataProcessLogin['token'],
                'user' => $this->renderUserInfos($dataProcessLogin['user']),
            ],
        ], 200);

    }

    /**
     * Check 2FA
     */
    private function Check2fa($seller){
        if ($seller && $this->twoFactorAuthService->is2FAEnabled($seller)) {
            // Return a response indicating 2FA is required
            return [
                'response' => 'pending',
                'message' => 'Two-factor authentication required',
                'result' => [
                    'requires2fa' => true,
                    'userId' => $seller->user_id
                ]
            ];
        }
    }

    private function validateUserPsw($email, $password){
         // Retrieve user by email
         $user = User::where('email', $email)->where('type','seller')->first();

         // Check if user exists
         if (!$user || !Hash::check($password, $user->password)) {
             return [
                 'response' => 'error',
                 'message' => 'Invalid credentials'
             ];
         }
         return [
            'response' => 'success',
            'user' => $user,
         ];
    }

    /**
     * Validate Login
     */
    private function validateLogin(Request $request){

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ];
        }
        // Return Success Response
        return [
            'response' => 'success',
            'email' => $request->email,
            'password' => $request->password,
        ];


    }

    /**
     * validate AutoL ogin
     */
    private function validateAutoLogin($token){
        // Find seller with matching token_login
        $seller = Sellers::where('token_login', $token)->first();

        // Check Exist Sellers
        if (!$seller) {
            return [
                'response' => 'error',
                'message' => 'Invalid auto-login token'
            ];
        }

        // Return Success Response
        return [
            'response' => 'success',
            'seller' => $seller,
        ];
    }

    /**
     * process Login Seller
     */
    public function processLoginSeller($seller,$user = []){
        // Get associated user
        $user = $user ? $user : User::findOrFail($seller->user_id);

        // Generate Passport token
        $token = $user->createToken('token')->accessToken;

        // Return Success Response
        return [
            'user' => $user,
            'token' => $token,
        ];
    }

    /**
     * empty Token Seller
     */
    private function emptyTokenSeller($seller){
        // Clear the token_login
        $seller->token_login = null;
        return $seller->save();
    }

    /**
     * Logout the authenticated user by revoking their access token.
     */
    public function logout(Request $request)
    {
       // Use auth('api') instead of $request->user()
        $user = auth('api')->user();

        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. User not authenticated.',
                'status'   => 401,
            ], 401);
        }

        // Revoke the user's token
        $user->token()->revoke();

        return response()->json([
            'response' => 'success',
            'message' => 'Logout successful',
        ], 200);
    }

    /**
     * render User Infos
     */
    private function renderUserInfos($user){
        // Retrieve the seller record
        return $this->FilterSellers->getSellers([
            "user_id" => $user->id,
            "first" => 1,
        ]);
    }
}