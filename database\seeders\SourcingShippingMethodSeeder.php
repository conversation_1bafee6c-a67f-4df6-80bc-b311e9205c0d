<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SourcingShippingMethod;

class SourcingShippingMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $shippingMethods = [
            'Air Freight',
            'Ocean',
        ];

        foreach ($shippingMethods as $method) {
            SourcingShippingMethod::create(['name' => $method]);
        }
    }
}