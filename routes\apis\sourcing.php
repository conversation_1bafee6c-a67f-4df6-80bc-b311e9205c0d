<?php

use App\Http\Controllers\Sourcing\SourcingInvoicesController;
use App\Http\Controllers\Sourcing\SourcingManagementController;
use Illuminate\Support\Facades\Route;

// Manage Sourcing Routes
Route::prefix('sourcing')->name('sourcing.')->group(function () {
    Route::get('invoices/list', [SourcingInvoicesController::class, 'list'])->name('list'); // List of invoices
    Route::get('invoices/details/{id}', [SourcingInvoicesController::class, 'details'])->name('invoice.details'); // List of invoices
    Route::get('/details/{id}', [SourcingManagementController::class, 'getRequestWithProducts'])->name('details'); // List of invoices
    Route::get('/list', [SourcingManagementController::class, 'list'])->name('RequestSroucinglist');
    Route::post('/store', [SourcingManagementController::class, 'store'])->name('store'); // List of invoices
    Route::delete('/delete/{id}', [SourcingManagementController::class, 'destroy'])->name('delete'); // List of invoices
    Route::post('/update/{id}', [SourcingManagementController::class, 'update'])->name('update'); // List of invoices

});