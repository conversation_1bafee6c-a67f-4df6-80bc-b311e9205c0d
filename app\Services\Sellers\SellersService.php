<?php
namespace App\Services\Sellers;
use App\Models\SellersPages;

class SellersService {

    public function __construct() {
       
    }

    /**
     * Get Current Seller Store
     */
    public function getStore($options = []) {
        $sellerId = $options['sellerId'];
        $rowStore = SellersPages::where("statut_validate","open")
                                    ->where('seller_id', $sellerId)
                                    ->orderBy('id','asc')
                                    ->first();

        // Return Store
        return $rowStore;
    }
}