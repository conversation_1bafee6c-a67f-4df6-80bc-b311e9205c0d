<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SourcingRequest extends Model
{
    //
    protected $table = 'sellers_sourcing_request';

	public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
	    'request_code',
	    'created_by',
	    'updated_by',
        'message',
        'status',
        'shipping_status',
        'payment_status',
        'reason_of_refuse',

	];

	protected $guarded = [];

	public function products()
	{
		return $this->hasMany(SourcingProduct::class, 'sourcing_request_id');
	}

}