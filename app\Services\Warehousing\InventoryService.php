<?php
namespace App\Services\Warehousing;

use App\Models\SellersStockOperations;
use App\Services\Orders\OrderFlowService;
use App\Services\Shipping\FilterWarehouses;
use App\Models\SellersStock;
use App\Models\Warehouses;
use App\Services\Shipping\FilterCountries;

class InventoryService{
    protected FilterWarehouses $FilterWarehouses;
    protected FilterProducts $FilterProducts;
    protected OrderFlowService $OrderFlowService;
    protected FilterCountries $FilterCountries;

    /**
     * Inject the FilterProducts service into the controller
     */
    public function __construct(){
        $this->FilterWarehouses = new FilterWarehouses();
        $this->FilterProducts = new FilterProducts();
        $this->OrderFlowService = new OrderFlowService();
        $this->FilterCountries = new FilterCountries();
    }

    /**
     * Load Product Inventory
     */
    public function loadProductInventory($rowProduct){
        $productInventory = $this->calculateInventoryStatus($rowProduct, ['loadDetails' => 1]);
        $inventoryResults = $productInventory['inventoryResults'] ?? null;

        // Préparer produits, entrepôts, pays
        [$listeProducts, $listeWarehouses, $listCountries] = $this->prepareProductContext($rowProduct);

        return $this->buildInventoryDetails($rowProduct, $listeProducts, $listeWarehouses, $inventoryResults, $listCountries);
    }

    /**
     * Prépare les produits, entrepôts et pays associés
     */
    private function prepareProductContext($rowProduct){
        // Liste des produits : soit les variantes, soit le produit lui-même
        $listeProducts = ($rowProduct->productType === "variable")
            ? $this->FilterProducts->getProducts([
                'parent' => $rowProduct->id,
                'noPaginate' => 1,
            ])
            : [$rowProduct];

        $listeProductsIds = array_map(fn($product) => $product->id, $listeProducts);

        $warehouseIds = SellersStock::whereIn('parent', $listeProductsIds)->pluck('warehouse_id')->all();
        $listeWarehouses = Warehouses::whereIn('id', $warehouseIds)->get();

        $countryIds = $listeWarehouses->pluck('country_id')->all();
        $listCountries = $this->FilterCountries->getCountries([
            'listeIds' => $countryIds,
            'noPaginate' => 1,
            'noFormatter' => 1,
        ]);

        return [$listeProducts, $listeWarehouses, $listCountries];
    }

    /**
     * Construit le détail d’inventaire regroupé par pays
     */
    private function buildInventoryDetails($rowProduct, $listeProducts, $listeWarehouses, $inventoryResults, $listCountries){
        $inventoryDetails = [];

        foreach ($listeProducts as $subProduct) {
            $details_warhouses = [];
            foreach ($listeWarehouses as $warehouse) {
                $leftQty = 0;

                foreach ($inventoryResults ?? [] as $inv) {
                    if ($rowProduct->productType === 'variable') {
                        foreach ($inv['inventoryResults'] ?? [] as $st1) {
                            if ($st1['productId'] == $subProduct->id && $st1['warehouseId'] == $warehouse->id) {
                                $leftQty += $st1['leftQty'];
                            }
                        }
                    } else {
                        if (($inv['productId'] ?? null) == $subProduct->id && $inv['warehouseId'] == $warehouse->id) {
                            $leftQty += $inv['leftQty'];
                        }
                    }
                }

                $details_warhouses[] = [
                    'warehouseId' => $warehouse->id,
                    'warehouseName' => $warehouse->name,
                    'countryId' => $warehouse->country_id,
                    'countryName' => $warehouse->country_name,
                    'leftQty' => $leftQty,
                ];
            }

            usort($details_warhouses, fn($a, $b) => (float)$b['leftQty'] <=> (float)$a['leftQty']);

            $details_countries = [];
            foreach ($listCountries as $country) {
                $leftQty = array_sum(array_column(
                    array_filter($details_warhouses, fn($wh) => $wh['countryId'] == $country->id),
                    'leftQty'
                ));

                $details_countries[] = [
                    'countryId' => $country->id,
                    'countryName' => $country->name,
                    'leftQty' => $leftQty,
                ];
            }

            usort($details_countries, fn($a, $b) => (float)$b['leftQty'] <=> (float)$a['leftQty']);

            $inventoryDetails[] = [
                'id' => $subProduct->id,
                'name' => $subProduct->name,
                'details' => $details_countries,
            ];
        }

        return $inventoryDetails;
    }


    /*
        calculate Inventory Status
    */
    public function calculateInventoryStatus($rowProduct, $options = []){
        // Initial params
        $loadDetails = $options['loadDetails'] ?? null;
        $inventoryResults = [];
        $productInventory = [];

        // Check Product
        if ($rowProduct->id ?? null) {
            $productType = $rowProduct->productType;

            if ($rowProduct->parent == 0 || is_null($rowProduct->parent) || $productType == "variant") {
                
                // Is Variable 
                if ($productType == "variable") {
                    $listeVariantes = $this->FilterProducts->getProducts([
                        'parent' => $rowProduct->id,
                        'noPaginate' => 1,
                        'noFormatter' => 1,
                    ]);
                          
                    if (count($listeVariantes ?? [])) {
                        foreach ($listeVariantes as $rowVariant) {
                            $stock_variante = $this->calculateInventoryStatus($rowVariant, ['loadDetails' => 1]);
                            $inventoryResults[] = $stock_variante;
                        }
                    }
                } else {
                    $listeStocks = $this->FilterProducts->getProducts([
                        'parent' => $rowProduct->id,
                        'noPaginate' => 1,
                        'noFormatter' => 1,
                    ]);
                    $inprogessQty = 0;
                    $delivredQty = 0;
                    $leftQty = 0;
                    $updateLeftQty = 0;
                    if (count($listeStocks ?? []) > 0) {
                        foreach ($listeStocks as $row_stock) {
                            $info_products = $this->calculateInventoryStatus($row_stock);
                            
                            $inprogessQty = $inprogessQty + $info_products['inprogessQty'];
                            $delivredQty = $delivredQty + $info_products['delivredQty'];
                            $leftQty = $leftQty + $info_products['leftQty'];
                            $updateLeftQty = $updateLeftQty + $info_products['updateLeftQty'];
                            $inventoryResults[] = $info_products;
                        }
                    }

                    $info = [
                        'inprogessQty' => $inprogessQty,
                        'delivredQty' => $delivredQty,
                        'leftQty' => $leftQty,
                        'updateLeftQty' => $updateLeftQty,
                    ];
                }
            } else {
                $total_manual_supply = 0;
                if ($rowProduct->shippingType == "firstmile") {
                    $delivredQty = $this->deliveredOrders($rowProduct, 1);
                    $total_manual_supply = $this->fetchStockOperations([
                        'productId' => $rowProduct->id,
                        'sumQty' => 1,
                        'type' => 'supply',
                    ]);

                    $totalQty = $delivredQty + $total_manual_supply;
                    $inprogessQty = $rowProduct->quantity;
                    $leftQty = $totalQty - $inprogessQty;
                    $updateLeftQty = $leftQty;
                } else {
                    $totalQty = $rowProduct->quantity;
                    $inprogessQty = Self::InProgressOrders($rowProduct);
                    $delivredQty = $this->deliveredOrders($rowProduct, 1);
                    $leftQty = $totalQty - $inprogessQty - $delivredQty;
                    $updateLeftQty = $totalQty - $delivredQty;
                }

                $productInventory= [
                    'stockId' => $rowProduct->id,
                    'stockName' => $rowProduct->name,
                    'productId' => $rowProduct->parent,
                    'warehouseId' => $rowProduct->warehouseId,
                    'totalQty' => $totalQty ?: 0,
                    'inprogessQty' => $inprogessQty ?: 0,
                    'delivredQty' => $delivredQty ?: 0,
                    'leftQty' => $leftQty ?: 0,
                    'updateLeftQty' => $updateLeftQty ?: 0,
                ];
            }


            if ($loadDetails == 1) {
                return [
                    'inventoryResults' => $inventoryResults,
                    'productType' => $productType,
                ];
            }

            return $productInventory;
        }
    }
    
    /*
        Handle inventory for a variable product (parent product with variants)
    */
    private function handleVariableProduct($rowProduct): array{
        $results = [];
        $variants = $this->FilterProducts->getProducts([
            'parent' => $rowProduct->id,
            'noPaginate' => 1,
            'noFormatter' => 1,
        ]);

        foreach ($variants ?? [] as $variant) {
            $results[] = $this->calculateInventoryStatus($variant, ['loadDetails' => 1]);
        }

        return $results;
    }

    /*
        Handle inventory aggregation for a variant group (children of a non-variable parent)
    */
    private function handleSimpleProduct($rowProduct, $loadDetails){
        $inventoryResults = [];
        $inprogessQty = $delivredQty = $leftQty = $updateLeftQty = 0;

        $stocks = $this->FilterProducts->getProducts([
            'parent' => $rowProduct->id,
            'noPaginate' => 1,
            'noFormatter' => 1,
        ]);

        foreach ($stocks ?? [] as $stock) {
            $info = $this->calculateInventoryStatus($stock);
            $inprogessQty += $info['inprogessQty'];
            $delivredQty  += $info['delivredQty'];
            $leftQty      += $info['leftQty'];
            $updateLeftQty += $info['updateLeftQty'];
            $inventoryResults[] = $info;
        }

        if ($loadDetails == 1) {
            return [
                'inventoryResults' => $inventoryResults,
                'productType' => $rowProduct->productType,
            ];
        }

        return [
            'inprogessQty' => $inprogessQty,
            'delivredQty' => $delivredQty,
            'leftQty' => $leftQty,
            'updateLeftQty' => $updateLeftQty,
        ];
    }

    /*
        Handle inventory for a single variant (individual stock row)
    */
    private function handleSingleVariant($rowProduct): array{
        if ($rowProduct->shippingType === "firstmile") {
            $delivredQty = $this->deliveredOrders($rowProduct, 1);
            $manualSupply = $this->fetchStockOperations([
                'productId' => $rowProduct->id,
                'sumQty' => 1,
                'type' => 'supply',
            ]);

            $totalQty = $delivredQty + $manualSupply;
            $inprogessQty = $rowProduct->quantity;
            $leftQty = $totalQty - $inprogessQty;
            $updateLeftQty = $leftQty;
        } else {
            $totalQty = $rowProduct->quantity;
            $inprogessQty = Self::InProgressOrders($rowProduct);
            $delivredQty = $this->deliveredOrders($rowProduct, 1);
            $leftQty = $totalQty - $inprogessQty - $delivredQty;
            $updateLeftQty = $totalQty - $delivredQty;
        }

        return [
            'stockId' => $rowProduct->id,
            'stockName' => $rowProduct->name,
            'productId' => $rowProduct->parent,
            'warehouseId' => $rowProduct->warehouseId,
            'totalQty' => $totalQty ?: 0,
            'inprogessQty' => $inprogessQty ?: 0,
            'delivredQty' => $delivredQty ?: 0,
            'leftQty' => $leftQty ?: 0,
            'updateLeftQty' => $updateLeftQty ?: 0,
        ];
    }

    /*
        Get delivered Orders
    */
    private function DeliveredOrders($rowProduct,$options = []){
        if ($rowProduct->id ?? null) {
            $results = $this->OrderFlowService->getProducts([
                'productId' => $rowProduct->id,
                'status' => "delivered",
                'count' => 1,
            ]);

            return $results;
        }
    }

    /*
        In Progess Ordrers
    */
    private function InProgressOrders($rowProduct,$options = []){
        if ($rowProduct->id ?? null) {
            // Initial params
            $loadDetails = $options['loadDetails'] ?? null;

            $results = [];

            if ($loadDetails) {
                $total_picked_up = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "picked-up",
                    'sumQty' => 1,
                ]);
                $total_in_transit = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "in-transit",
                    'sumQty' => 1,
                ]);
                $total_shipped = $this->OrderFlowService->getProducts([
                    'product_id' => $rowProduct->id,
                    'status' => "shipped",
                    'sumQty' => 1,
                ]);
                $total_undelivered_not_pikedup = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "undelivered_not_pikedup",
                    'sumQty' => 1,
                ]);

                $results = [
                    'total_picked_up' => $total_picked_up,
                    'total_in_transit' => $total_in_transit,
                    'total_shipped' => $total_shipped,
                    'total_undelivered_not_pikedup' => $total_undelivered_not_pikedup,
                ];
            } else {
                $results = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "encours",
                    'sumQty' => 1,
                ]);
            }

            return $results;
        }
    }

   /**
     * Get list of operations with optional filters and modes.
     */
    public function fetchStockOperations(array $data = []){
        $productId     = $data['productId']     ?? null;
        $type          = $data['type']           ?? null;
        $count         = $data['count']          ?? null;
        $limit         = $data['limit']          ?? null;
        $offset        = $data['offset']         ?? null;
        $sumQty = $data['sumQty'] ?? null;

        $query = SellersStockOperations::orderByDesc('id');

        if ($productId) {
            $query->where('product_id', $productId);
        }

        if ($type && $type !== 'all') {
            $query->where('operation_type', $type);
        }

        if ($sumQty == 1) {
            if (!$type) {
                $query->whereNotIn('operation_type', ['supply']);
            }
            return $query->sum('quantity');
        }

        if ($limit) { $query->limit($limit); }

        if ($offset) {
            $query->offset($offset);
        }

        if ($count == 1) {
            return $query->count();
        }

        return $query->get();
    }



}