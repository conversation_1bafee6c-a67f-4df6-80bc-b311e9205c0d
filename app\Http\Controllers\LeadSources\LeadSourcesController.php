<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\SellersAutoImports;
use App\Services\LeadSource\FilterLeadSource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LeadSourcesController extends Controller{
    protected FilterLeadSource $FilterLeadSource;
    /**
     * Inject the services into the controller
     */
    public function __construct(){
        $this->FilterLeadSource = new FilterLeadSource();

    }

    /**
     * Retrieve and return a list of filtered orders.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse{
        // Retrieve the list of orders based on the provided filters
        $result = $this->FilterLeadSource->getLeadSources($request->all());

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $result['items'] ?? [],
            'paginate' => [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ],
        ]);

    }

    /**
     * Delete a lead source import
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function delete(Request $request): JsonResponse
    {
        // Validate request using query parameters
        if (!$request->query('import_id') || !$request->query('api_type')) {
            return response()->json([
                'response' => 'error',
                'message' => 'Import ID and API type are required'
            ], 400);
        }


        // Delete the lead source import using query parameters
        $result = $this->FilterLeadSource->deleteLeadSourceImport(
            $request->query('import_id'),
            $request->query('api_type')
        );

        if (!$result['success']) {
            return response()->json([
                'response' => 'error',
                'message' => $result['message']
            ], 404);
        }

        // Get updated list after deletion
        $listResult = $this->list($request);
        $listData = $listResult->getData(true);

        return response()->json([
            'response' => 'success',
            'message' => $result['message'],
            // 'account_deleted' => $result['account_deleted'] ?? false,
            'result' => $listData['result'] ?? [],
            'paginate' => $listData['paginate'] ?? []
        ]);
    }


}