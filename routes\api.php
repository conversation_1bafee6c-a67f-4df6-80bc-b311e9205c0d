<?php
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->group(function () {
    require 'apis/auth.php'; // Auth API Routes
    require 'apis/leadSources/api.php'; // Shopify, YouCan .. API Routes

    // Group routes with 'auth.token' middleware
    Route::middleware('auth.token')->group(function () {
        require 'apis/general.php';  // General API Routes
        require 'apis/orders.php';  // Orders API Routes
        require 'apis/invoices.php';  // Invoices API Routes
        require 'apis/sourcing.php'; // Sourcing API Routes
        require 'apis/products.php'; // Products API Routes
        require 'apis/attributes.php'; // Attribute API Routes
        require 'apis/kpis.php'; // Kpis API Routes
        require 'apis/tools.php'; // Tools API Routes
    });
});