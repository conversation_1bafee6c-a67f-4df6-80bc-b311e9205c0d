<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersStockUpsellsPrices extends Model
{
    //
     protected $table = 'sellers_stock_upsells_prices';

	public $timestamps = true;

	protected $fillable = [
	    'product_id',
	    'product_name',
	    'upsell_id',
	    'upsell_name',
	    'currency',
	    'price',
	];

	// Prices
	public function upsell(){
		return $this->hasMany(SellersStockUpsells::class, 'upsell_id');
	}

	protected $guarded = []; 
}
    