<?php
namespace App\Services\Fields;
use App\Services\Fields\FieldsService;
use GlobalHelper;
use MediaHelper;
use SellersHelper;

class FormatterProducts {

    protected $FieldsService;
    protected $currentSeller;
    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Format the orders based on provided options
     */
    public function formatterResult(array $options = []): array|null{
        // Extract options
        $first = $options['first'] ?? null;
        $items = $options['items'] ?? [];

        // Convert to array using GlobalHelper
        $items = GlobalHelper::ConvertToArray(['liste' => $items]);

        // Return empty array if no items are provided
        if (empty($items)) {
            return $first === 1 ? null : ['items' => []];
        }

        // Format the items
        $formattedItems = array_map(function ($row) {
            $groupedData = $this->FieldsService->GroupeFields('products', $row);
            return $this->FormatterProduct($groupedData);
        }, $items);

        // Return either the first item or all formatted items
        return $first === 1 ? ($formattedItems[0] ?? null) : ['items' => $formattedItems];
    }

    /**
     * Formatter for an individual order
     */
    private function FormatterProduct(array $row_product): array{
        // Format Media
        $row_product = $this->FormatterMedia($row_product);

        // return order
        return $row_product;
    }

    /**
     * Formatter for the product details within an order
     */
    private function FormatterMedia(array $row_product): array{

        // Check if both 'goodsDescription' and 'sku' exist in the array
        if (array_key_exists('mainImage', $row_product)) {
            $row_product['mainImage'] = MediaHelper::getFileUrl(["tableName" => "sellers_stock","tableId" => $row_product['id']]);
        }
        return $row_product;
    }
}