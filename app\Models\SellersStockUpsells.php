<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersStockUpsells extends Model
{
    //
     protected $table = 'sellers_stock_upsells';

	public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
	    'product_id',
	    'product_name',
	    'upsell_name',
	    'total_free',
	    'total_paid',
	];

	/**
	 * Product
	 */
	public function product(){
        return $this->belongsTo(SellersStock::class, 'product_id', 'id');
    }

	// Prices
	public function prices(){
		return $this->hasMany(SellersStockUpsellsPrices::class, 'upsell_id');
	}

	protected $guarded = []; 
}
    