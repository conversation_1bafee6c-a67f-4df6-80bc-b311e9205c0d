<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersLivraison extends Model
{
    //
     protected $table = 'sellers_livraison';

	public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'from_country_id',
	    'from_country_name',
	    'country_id',
	    'country_name',

	    'base_shipping',
	    'firstmile_price',
	    'clearance_price',
	    'custom_surcharge',
	    'return_price',
	    'fulfillment',
	    'lastmile_type',
	    'lastmile_fixedcod',
	    'lastmile_fixedprepaid',
	    'lastmile_firstweight',
	    'lastmile_pricecod',
	    'lastmile_additionalweight',
	    'lastmile_additionalprice',
	    'lastmile_priceprepaid',
	    'cod_declared_value',
	    'cod_pourc_value',
	    'cod_flaterate',
	    'cod_type_calcul',
	    'clearance_declared_value',
	    'clearance_pourc_value',
	    'clearance_flaterate',
	    'clearance_type_calcul',
	    'invoiced',
	];

	protected $guarded = []; 
}
    