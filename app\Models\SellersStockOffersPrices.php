<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersStockOffersPrices extends Model
{
    //
     protected $table = 'sellers_stock_offers_prices';

	public $timestamps = true;

	protected $fillable = [
	    'product_id',
	    'product_name',
	    'offer_id',
	    'offer_name',
	    'currency',
	    'price',
	];

	public function offer() {
        return $this->belongsTo(SellersStockOffers::class, 'offer_id', 'id');
    }

	protected $guarded = []; 
}
    