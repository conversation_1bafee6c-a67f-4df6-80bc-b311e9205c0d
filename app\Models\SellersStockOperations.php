<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersStockOperations extends Model
{
    //
     protected $table = 'sellers_stock_operations';

	public $timestamps = true;

	protected $fillable = [
	    'product_id',
	    'product_name',
	    'quantity',
	    'to_seller_id',
	    'to_seller_name',
	    'to_product_id',
	    'to_product_name',
	    'operation_type',
	    'operation_description',
	    'origin_operation',
	    'sourcing_invoice_id',
	    'comment',
	    'created_by',
	];

	protected $guarded = []; 
}
    