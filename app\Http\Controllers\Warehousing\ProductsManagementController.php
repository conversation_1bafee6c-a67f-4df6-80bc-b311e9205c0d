<?php

namespace App\Http\Controllers\Warehousing;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\SellersStock;
use App\Services\Fields\FieldsService;
use App\Services\Validator\ProductsValidator;
use App\Services\Warehousing\FilterProducts;
use Illuminate\Support\Facades\DB;
use MediaHelper;
use SellersHelper;

class ProductsManagementController extends Controller{
    protected ProductsValidator $productsValidator;
    protected FieldsService $FieldsService;
    protected ProductsController $productsController;
    protected FilterProducts $FilterProducts;
    protected $currentSeller;

    public function __construct(){
        $this->productsValidator = new ProductsValidator();
        $this->FieldsService = new FieldsService();
        $this->productsController = new ProductsController();
        $this->FilterProducts = new FilterProducts();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Create a new product.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse{

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'products',
            'option' => 'createValidation',
            'data' => $request->all(),
        ]);
        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'store',
            'type' => 'products',
            'data' => $request->all(),
            'AppendParams' => [
                'seller_id' =>  $this->currentSeller->id,
                'seller_name' =>  $this->currentSeller->fullname,
            ]
        ]);


        // Create the product
        $product = SellersStock::create($processData);
        // dd($product,$processData);

        // Save Media
        $this->saveProductImage($request,$product->id);

        //get product details
        $productDetails = $this->productDetails($request,$product->id);

        // Return the response with the created product and its details
        return response()->json([
             'response' => 'success',
             'message' => __('products.product_created_successfully'),
             'result' => $productDetails
         ], 201);
    }

    /**
     * Update an existing product.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse{

        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $id,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'products',
            'option' => 'updateValidation',
            'data' => $request->all(),
            'replaceParams' => [
                "{id}" => $id
            ]
        ]);

        if ($resultValidator['response'] == "error") { return response()->json($resultValidator, 400);}

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'update',
            'type' => 'products',
            'data' => $request->all(),
        ]);

        //find product instance
        $product = SellersStock::find($id);

        // Update the product
        $product->update($processData);

        // Save Media
        $this->saveProductImage($request,$product->id);

        // Update children arabic_name
        $this->updateChildren($product, $processData);
        //get product details
        $productDetails = $this->productDetails($request,$product->id);

         // Return the response with the created product and its details
         return response()->json([
             'response' => 'success',
             'message' => __('products.product_updated_successfully'),
             'result' => $productDetails
         ], 201);
    }

    /**
     * updateChildren after update product
     */
    private function updateChildren($product, $processData){
        if (isset($processData['arabic_name'])) {
            DB::table('sellers_stock')
                ->where('parent', $product->id)
                ->update([
                    'arabic_name' => $product->arabic_name,
                ]);

        }
    }

    /**
     * Get product details from products controller
     */
    private function productDetails($request,$id){
        // Call the details method (for product)
        $detailsResponse = $this->productsController->details($request, $id);

        // Extract the data from the JsonResponse object
        return $detailsResponse->getData(true)['result'];
    }

    /**
     * Delete a product.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $id,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // // Check Have Stock
        // if($this->checkHaveStock($id)){
        //     return response()->json(['response' => 'error','message' => __('products.product_deleted_fail_stock')]);
        // }

        // Find the product
        $product = SellersStock::find($id);
        $product->update(['is_archive' => 1]);


        // Return Response
        return response()->json([
            'response' => 'success',
            'message' => __('products.product_deleted_successfully'),
        ]);
    }

    /**
     * Restore a product from archive.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function unarchive($id): JsonResponse
    {
        // Validate Product
        $validateProduct = $this->productsValidator->validateProduct(['id' => $id, 'layout' => 'details', 'archive' => 1]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }

        // Find the product
        $product = SellersStock::find($id);
        $product->update(['is_archive' => 0]);

        // Return Response
        return response()->json([
            'response' => 'success',
            'message' => __('products.product_restored_successfully'),
        ]);
    }
    /**
     * Check have Stock
     */
    private function checkHaveStock($id){
        $listeStock = $this->FilterProducts->getProducts([
            'parent' => $id,
            'count' => 1,
        ]);
        return $listeStock > 0 ? true : false;
    }

    /**
     * saveProductImage
     */
    private function saveProductImage(Request $request, $productId){

        MediaHelper::saveMedia([
            'request' => $request,
            'imageSlug' => "image",
            'fileName' => "image",
            'tableName' => 'sellers_stock',
            'tableId' => $productId,
        ]);
    }


}