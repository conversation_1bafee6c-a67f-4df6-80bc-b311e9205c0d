<?php
namespace App\Services\Sellers;

use App\Models\Sellers;
use App\Services\Fields\FieldsService;
use App\Services\Fields\FormatterSellers;
use App\Traits\FilterQuery;

class FilterSellers {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $FormatterSellers;
    protected $columnsType;
    use FilterQuery;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->FormatterSellers = new FormatterSellers();

        // Table name
        $this->baseTable = 'sellers';
        $this->columnsType = 'sellers';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getSellers(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // General Conditions
        $this->applyGeneralFilters();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();
        
        // Fetch and return the final filtered results
        return $this->fetchResults($this->FormatterSellers);
    }

   /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = Sellers::query();
    }

    /*
        General Conditions
    */
    private function applyGeneralFilters(){
        $id = $this->data["id"] ?? NULL;
        $user_id = $this->data["user_id"] ?? NULL;

        // By ID
        if($id){ $this->query->where('id',$id); }

        // By User ID
        if($user_id){ $this->query->where('user_id',$user_id); }
    }   
}