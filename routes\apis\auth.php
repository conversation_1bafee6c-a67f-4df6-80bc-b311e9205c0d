<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\OnboardingController;
use App\Http\Controllers\Auth\ProfileController;
use App\Http\Controllers\Auth\SellerController;
use App\Http\Controllers\Auth\TwoFactorAuthController;
use Illuminate\Support\Facades\Route;

Route::post('login', [AuthController::class, 'login']);
Route::post('autoLogin/{token}', [AuthController::class, 'autoLogin']);
Route::post('forgotpassword', [OnboardingController::class, 'forgotPassword']);
Route::post('resetCodeCheck', [OnboardingController::class, 'checkResetCode']);
Route::post('resetPassword', [OnboardingController::class, 'resetPassword']);
Route::post('verify2fa', [TwoFactorAuthController::class, 'verify'])->name('verify2fa'); // Verify and enable 2FA
// Manage Profile Routes
Route::middleware('auth.token')->group(function () {
    // Logout
    Route::post('logout', [AuthController::class, 'logout']);

    // Profil Routes
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show'); // Get profile
        Route::put('/', [ProfileController::class, 'update'])->name('update'); // Update profile
        Route::put('/password', [ProfileController::class, 'updatePassword'])->name('updatePassword'); // Update profile
        Route::get('/fees', [SellerController::class, 'fees'])->name('fees'); // Seller Fees

    });

    // Two-Factor Authentication Routes
    Route::prefix('2fa')->name('2fa.')->group(function () {
        Route::get('/status', [TwoFactorAuthController::class, 'status'])->name('status'); // Get 2FA status
        Route::post('/toggle', [TwoFactorAuthController::class, 'Toggle2FA'])->name('toggle'); // Setup 2FA
        
    });
});