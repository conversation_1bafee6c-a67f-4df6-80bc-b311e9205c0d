<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('seller_sourcing_product', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('seller_id');
            $table->string('seller_name');
            $table->unsignedBigInteger('sourcing_request_id');
            $table->string('sourcing_request_code');
            $table->string('arabic_name')->nullable();
            $table->json('attribute_names')->nullable();
            $table->string('reference')->nullable();
            $table->string('reference_code')->nullable();
            $table->decimal('weight', 10, 2)->nullable();
            $table->decimal('width', 10, 2)->nullable();
            $table->decimal('height', 10, 2)->nullable();
            $table->decimal('length', 10, 2)->nullable();
            $table->string('statut')->nullable();
            $table->boolean('is_archive')->default(false);
            $table->text('description_callcenter')->nullable();
            $table->string('product_link')->nullable();
            $table->string('product_video')->nullable();
            $table->string('product_seller_image')->nullable();
            $table->string('product_found_image')->nullable();
            $table->string('parent')->nullable();
            $table->integer('total_products')->default(0);
            $table->unsignedBigInteger('warehouse_id')->nullable();
            $table->string('warehouse_name')->nullable();
            $table->string('shipping_by')->nullable();
            $table->unsignedBigInteger('confirmed_by')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->unsignedBigInteger('created_by');
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->string('product_type')->nullable();
            $table->string('hscode')->nullable();
            $table->string('categorie_name')->nullable();
            $table->boolean('is_test')->default(false);
            $table->string('process_mode')->nullable();
            $table->string('origin_country')->nullable();
            $table->string('destination_country')->nullable();
            $table->integer('quantity')->default(0);
            $table->string('shipping_method')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();

            $table->foreign('sourcing_request_id')
                  ->references('id')
                  ->on('sellers_sourcing_request')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('seller_sourcing_product');
    }
};