<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Warehouses extends Model
{
	//
	protected $table = 'warehouse';

	public $timestamps = true;

	protected $fillable = [
		'name',
		'country_id',
		'country_name',
		'city_id',
		'city_name',
		'description',
		'statut',
		'reference',
		'reference_code',
		'created_by',
		'type',
		'manager_id',
		'address',
		'latitude',
		'longitude',
		'manager_name',
		'email',
		'password',
	];
	
	protected $guarded = [];
}
