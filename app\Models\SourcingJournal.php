<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SourcingJournal extends Model
{
    protected $table = 'sourcing_journals';

    protected $fillable = [
        'user_id',
        'user_name',
        'sourcing_id',
        'sourcing_code',
        'sourcing_seller',
        'sourcing_status',
        'action',
    ];

    public $timestamps = true;

    /**
     * Get the sourcing request that owns the journal entry.
     */
    public function sourcingRequest()
    {
        return $this->belongsTo(SourcingRequest::class, 'sourcing_id');
    }

    /**
     * Get the user who performed the action.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
