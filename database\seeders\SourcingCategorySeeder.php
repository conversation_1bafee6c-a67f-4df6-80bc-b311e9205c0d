<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\SourcingCategory;

class SourcingCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        $categories = [
            'Electronics',
            'Audio Devices',
            'Fitness Gear',
            'Home Appliances',
            'Smartphones',
            'Laptops',
            'Tablets',
            'Headphones',
        ];

        foreach ($categories as $category) {
            SourcingCategory::create(['name' => $category]);
        }
    }
}