<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersColisFollowupHistory extends Model
{
    //
     protected $table = 'sellers_colis_followup_history';

	public $timestamps = true;

	protected $fillable = [
	    'colis_id',
	    'colis_barecode',
	    'action_name',
	    'action_date',
	    'action_motif',
	    'colis_tracking_number',
	    'feedbacktype',
	    'rejectreason',
	    'scheduledate',
	    'created_by',
	    'created_by_id',
	    'comment',
	    'isanswer',
	    'invoice_id',
	    'invoice_num',
	    'is_paid',
		'canal'
	];

	protected $guarded = []; 
}
    