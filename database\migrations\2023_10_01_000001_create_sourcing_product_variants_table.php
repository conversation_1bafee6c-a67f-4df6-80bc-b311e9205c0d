<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSourcingProductVariantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seller_sourcing_product_variants', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('seller_id');
            $table->string('seller_name');
            $table->unsignedBigInteger('sourcing_request_id');
            $table->string('sourcing_request_code');
            $table->unsignedBigInteger('product_id');
            $table->string('name');
            $table->string('value');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('product_id')->references('id')->on('seller_sourcing_product')->onDelete('cascade');
            $table->foreign('sourcing_request_id')->references('id')->on('sellers_sourcing_request')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('seller_sourcing_product_variants');
    }
}