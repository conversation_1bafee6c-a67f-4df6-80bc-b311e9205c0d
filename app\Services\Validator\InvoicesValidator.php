<?php

namespace App\Services\Validator;

use App\Services\Invoices\FilterInvoices;

class InvoicesValidator{

    /**
     * Validate Product
     */
    public function validateInvoice(array $searchData = [], array $options = []): array {
        // Check if ID exists
        if (!($searchData['id'] ?? null)) {
            return $this->errorResponse(__('invoices.invoice_id_is_required'));
        }

        // Retrieve Product
        $rowInvoice = $this->getInvoice($searchData);
        if (!$rowInvoice) {
            return $this->errorResponse(__('invoices.invoice_not_found'));
        }

        return [
            'response' => 'success',
            'rowInvoice' => $rowInvoice,
        ];
    }

    /**
     * get invoice
     */
    private function getInvoice(array $searchData){
        $searchData['first'] = 1;
        return (new FilterInvoices())->getInvoices($searchData);
    }

    /**
     * Generate a standardized error response.
     */
    private function errorResponse(string $message): array{
        return [
            'response' => 'error',
            'message' => $message,
        ];
    }
}