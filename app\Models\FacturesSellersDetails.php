<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FacturesSellersDetails extends Model
{
    protected $table = 'factures_sellers_details';

	public $timestamps = true;

	protected $fillable = [
		'facture_id',
        'facture_num',
        'seller_id',
        'seller_name',
        'poids',
        'valeur',
        'mode_livraison',
        'colis_id',
        'colis_barecode',
        'call_id',
        'call_date',
        'call_username',
        'call_comment',
        'colis_price',
        'paid_by',
        'colis_price_livraison',
        'colis_price_return',
        'price_fulfillment',
        'firstmile_price',
        'custom_surcharge',
        'price_confirmeddelivered',
        'method_paiement',
        'final_price_colis',
        'final_comission',
        'final_credit',
        'colis_statut',
        'facture_type',
        'is_confirmed',
        'confirmed_at',
        'shipping_price',
        'price_callcenter',
        'colis_price_upsell',
        'is_upsell',
        'order_num',
        'price_origine',
        'currency',
        'from_name',
        'to_name',
        'vat_price',
        'cod_fees_declared',
        'cod_fees_deducted',
        'cod_fees',
        'clearance_fees',
        'is_manualy',
	];

    /**
     * load Order
     */
    public function order(){
        return $this->belongsTo(SellersColis::class, 'colis_id', 'id')->select(['id', 'order_num','barecode','shipped_at']);
    }

	protected $guarded = []; 
}
    