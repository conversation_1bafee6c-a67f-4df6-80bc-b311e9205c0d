<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersColisProducts extends Model
{
    //
     protected $table = 'sellers_colis_products';

	public $timestamps = true;

	protected $fillable = [
	    'colis_id',
	    'colis_barecode',
	    'colis_statut',
	    'seller_id',
	    'page_id',
	    
	    'main_product_id',
	    'main_product_sku',
	    'main_product_name',
	    
	    'variante_id',
	    'variante_name',
	    'variante_sku',
	    
	    'product_id',
	    'product_image',
	    'product_sku',
	    'name',
	    'description',
	    'price',
	    'price_livraison',
	    'weight',
	    'width',
	    'height',
	    'length',
	    'gross_weight',
	    'volumetric_weight',
	    'from_stock',
	    'statut_product',
	    'comment_client',
	    'comment_admin',
	    'statut_validate',
	    'upsell',
	    'upsell_by',
	  
	    'size',
	    'color',
	    'quantity',

	     'created_by',
	    'updated_by',
	    'deleted_by',

	];

	protected $guarded = []; 
}
    