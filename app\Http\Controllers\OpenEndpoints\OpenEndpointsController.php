<?php

namespace App\Http\Controllers\OpenEndpoints;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\User;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Symfony\Component\Process\Process;

class OpenEndpointsController extends Controller
{
    /**
     * Add two_factor columns to sellers table
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function addTwoFactorColumns()
    {
        try {
            // Check if columns already exist
            if (Schema::hasColumn('sellers', 'two_factor_enabled') && Schema::hasColumn('sellers', 'two_factor_secret')) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Columns already exist in the sellers table'
                ], 400);
            }

            // Execute direct MySQL query to add columns
            DB::statement('ALTER TABLE sellers ADD COLUMN two_factor_enabled BOOLEAN DEFAULT false');
            DB::statement('ALTER TABLE sellers ADD COLUMN two_factor_secret VARCHAR(255) NULL');

            return response()->json([
                'response' => 'success',
                'message' => 'Two-factor columns added successfully to sellers table'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to add columns: ' . $e->getMessage()
            ], 500);
        }
    }


    public function listDatabaseTables()
    {
        try {
            $tables = DB::select('SHOW TABLES');

            $dbName = DB::getDatabaseName();
            $key = 'Tables_in_' . $dbName;

            $tableNames = array_map(function ($table) use ($key) {
                return $table->$key;
            }, $tables);

            return response()->json([
                'response' => 'success',
                'tables' => $tableNames
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to list tables: ' . $e->getMessage()
            ], 500);
        }
    }

    public function createResetCodePasswordTable()
    {
        try {
            // Check if the table already exists
            if (Schema::hasTable('reset_code_passwords')) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Table reset_code_passwords already exists'
                ], 400);
            }

            // Create the table
            Schema::create('reset_code_passwords', function (Blueprint $table) {
                $table->id();
                $table->string('email');
                $table->string('code');
                $table->timestamp('created_at')->nullable();
            });

            return response()->json([
                'response' => 'success',
                'message' => 'Table reset_code_passwords created successfully'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to create table: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update test seller email and password
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateTestSeller()
    {
        try {
            // Find the seller by email
            $seller = Sellers::where('email', '<EMAIL>')->first();

            if (!$seller) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Seller <NAME_EMAIL> not found'
                ], 404);
            }

            // Update seller email
            $seller->email = '<EMAIL>';
            $seller->save();

            // Find and update the associated user
            $user = User::find($seller->user_id);

            if (!$user) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Associated user not found'
                ], 404);
            }

            // Update user email and password
            $user->email = '<EMAIL>';
            $user->password = Hash::make('<EMAIL>');
            $user->save();

            return response()->json([
                'response' => 'success',
                'message' => 'Test seller updated successfully',
                'data' => [
                    'email' => '<EMAIL>',
                    'password' => '<EMAIL>'
                ]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to update test seller: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Install Google2FA packages using Composer
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function installGoogle2faPackages()
    {
        try {
            // Create a new process to run the composer require command
            $process = new Process([
                'composer',
                'require',
                'pragmarx/google2fa-laravel',
                'pragmarx/google2fa-qrcode'
            ]);

            // Set a timeout of 300 seconds (5 minutes) as composer commands can take time
            $process->setTimeout(300);

            // Run the process
            $process->run();

            // Check if the process was successful
            if (!$process->isSuccessful()) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Failed to install Google2FA packages: ' . $process->getErrorOutput()
                ], 500);
            }

            return response()->json([
                'response' => 'success',
                'message' => 'Google2FA packages installed successfully',
                'output' => $process->getOutput()
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to install Google2FA packages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Publish Google2FA service provider
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function publishGoogle2faProvider()
    {
        try {
            // Create a new process to run the artisan vendor:publish command
            $process = new Process([
                'php',
                'artisan',
                'vendor:publish',
                '--provider=PragmaRX\Google2FALaravel\ServiceProvider'
            ]);

            // Set a timeout of 60 seconds
            $process->setTimeout(60);

            // Run the process
            $process->run();

            // Check if the process was successful
            if (!$process->isSuccessful()) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Failed to publish Google2FA provider: ' . $process->getErrorOutput()
                ], 500);
            }

            return response()->json([
                'response' => 'success',
                'message' => 'Google2FA provider published successfully',
                'output' => $process->getOutput()
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to publish Google2FA provider: ' . $e->getMessage()
            ], 500);
        }
    }
}