<?php
namespace App\Services\Orders;

use App\Models\Cities;
use App\Models\Citieskhalij;
use App\Models\Districtkhalij;
use App\Models\ShippingCompanies;
use Illuminate\Support\Facades\Cache;

class OrderService{
    protected OrderFlowService $OrderFlowService;
   
    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->OrderFlowService = new OrderFlowService();
    }

    /**
     * Shipping Company
    */
    public function shippingCompanyName($key){
        $listeShippingCompanies = Cache::remember('listeShippingCompanies', now()->addHours(12), function () {
            return ShippingCompanies::where('status', 'enabled')->pluck('name', 'slug')->all();
        });

        return $listeShippingCompanies[$key] ?? $key;
    }

    /*
       extract Address Details
    */
    public static function extractAddressDetails($rowOrder){
        $cityId = $rowOrder?->consignee->cityId;
        $rowCity = $cityId ? Districtkhalij::find($cityId) : null;
        $province = $rowCity?->city_id ? Citieskhalij::find($rowCity->city_id) : null;
        $country = $province?->country_id ? Cities::find($province->country_id) : null;

        return [
            'countryId'      => $country?->id ?? null,
            'countryName'    => $country?->name ?? null,
            'countryCode'    => $country?->slugan ?? null,
            'city'       => preg_replace('/\s+/', ' ', trim($rowCity?->naqel_name ?: $rowCity?->default_name ?: '')),
            'cityCode'       => $rowCity?->default_code ?? null,
            'province'   => $province?->city_name ?? $rowOrder?->consignee->province ?? null,
            'shortAddress'   => $rowOrder?->consignee->shortAddress,
            'houseNumber'    => $rowOrder?->consignee->houseNumber,
            'nearestPlace'   => $rowOrder?->consignee->nearestPlace,
            'street'     => $rowOrder?->consignee->street,
            'area'       => $rowOrder?->consignee->area,
            'zipCode'        => $rowCity?->zipcode ?? $rowOrder?->consignee->zipcode ?? null,
        ];
    }

    /*
       extract Products Details
    */
    public function extractProductsDetails($rowOrder){
        // Fetch associated products using the service
        $products = $this->OrderFlowService->getProducts(['orderId' => $rowOrder->id]);

        $names = [];
        $skus = [];
        $quantities = [];

        // Extract and format data from each product
        if($products){foreach ($products as $product) {
            $names[] = $product->name ?: $product->main_product_name;
            $skus[] = $product->main_product_sku;
            $quantities[] = $product->quantity ?: 1;
        }}

        // Return concatenated strings (or null if empty)
        return [
            "products" => implode("/", array_unique($names)) ?: null,
            "sku"     => implode("/", array_unique($skus)) ?: null,
            "qty"     => implode("/", array_unique($quantities)) ?: null,
        ];
    }
}